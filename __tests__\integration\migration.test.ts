import { prisma } from '@/lib/prisma'
import { CategoryService } from '@/lib/models/category'
import { ProductService } from '@/lib/models/product'
import { SalesTransactionService } from '@/lib/models/sales-transaction'
import { InventoryService } from '@/lib/models/inventory'

// Mock Prisma for integration tests
jest.mock('@/lib/prisma')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('Migration Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Database Schema Validation', () => {
    it('should have all required tables and relationships', async () => {
      // Mock the database schema check
      mockPrisma.$queryRaw.mockResolvedValue([
        { table_name: 'Category' },
        { table_name: 'Product' },
        { table_name: 'PackagingType' },
        { table_name: 'Inventory' },
        { table_name: 'User' },
        { table_name: 'SalesTransaction' },
        { table_name: 'SalesTransactionItem' },
        { table_name: 'StockMovement' }
      ])

      const tables = await mockPrisma.$queryRaw`
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
      `

      const tableNames = (tables as any[]).map(t => t.table_name)
      
      expect(tableNames).toContain('Category')
      expect(tableNames).toContain('Product')
      expect(tableNames).toContain('PackagingType')
      expect(tableNames).toContain('Inventory')
      expect(tableNames).toContain('User')
      expect(tableNames).toContain('SalesTransaction')
      expect(tableNames).toContain('SalesTransactionItem')
      expect(tableNames).toContain('StockMovement')
    })
  })

  describe('Data Migration Workflow', () => {
    it('should migrate categories successfully', async () => {
      const mockCategories = [
        { id: 1, name: 'Candy', description: 'Sweet treats', createdAt: new Date(), updatedAt: new Date() },
        { id: 2, name: 'Drinks', description: 'Beverages', createdAt: new Date(), updatedAt: new Date() },
        { id: 3, name: 'Personal Care', description: 'Personal care items', createdAt: new Date(), updatedAt: new Date() }
      ]

      mockPrisma.category.findMany.mockResolvedValue(mockCategories)
      mockPrisma.category.count.mockResolvedValue(3)

      const result = await CategoryService.getAll({
        page: 1,
        limit: 50,
        search: '',
        sortBy: 'name',
        sortOrder: 'asc'
      })

      expect(result.data).toHaveLength(3)
      expect(result.data[0].name).toBe('Candy')
      expect(result.pagination.totalItems).toBe(3)
    })

    it('should migrate products with relationships', async () => {
      const mockProducts = [
        {
          id: 1,
          name: 'Chocolate Bar',
          categoryId: 1,
          packagingDescription: '50g bar',
          pricePerPackage: 2.50,
          unitsPerPackage: 1,
          packagingTypeId: 1,
          unitPrice: 2.50,
          sku: 'CHOC001',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          category: { id: 1, name: 'Candy' },
          packagingType: { id: 1, name: 'Bar' },
          inventory: { currentStock: 100 }
        }
      ]

      mockPrisma.product.findMany.mockResolvedValue(mockProducts as any)
      mockPrisma.product.count.mockResolvedValue(1)

      const result = await ProductService.getAll({
        page: 1,
        limit: 20,
        search: '',
        sortBy: 'name',
        sortOrder: 'asc'
      })

      expect(result.data).toHaveLength(1)
      expect(result.data[0].name).toBe('Chocolate Bar')
      expect(result.data[0].category.name).toBe('Candy')
      expect(result.data[0].currentStock).toBe(100)
    })

    it('should handle sales transactions with inventory updates', async () => {
      const mockTransaction = {
        id: 1,
        transactionNumber: 'TXN-001',
        userId: 1,
        totalAmount: 10.00,
        taxAmount: 1.00,
        discountAmount: 0.00,
        finalAmount: 11.00,
        paymentMethod: 'CASH',
        paymentReceived: 15.00,
        changeAmount: 4.00,
        status: 'COMPLETED',
        createdAt: new Date(),
        updatedAt: new Date(),
        user: { fullName: 'John Doe' },
        items: [
          {
            id: 1,
            productId: 1,
            quantity: 2,
            unitPrice: 5.00,
            totalPrice: 10.00,
            product: { name: 'Chocolate Bar', sku: 'CHOC001' }
          }
        ]
      }

      mockPrisma.$transaction.mockResolvedValue(mockTransaction as any)

      const transactionData = {
        items: [
          {
            productId: 1,
            quantity: 2,
            unitPrice: 5.00
          }
        ],
        paymentMethod: 'CASH' as const,
        paymentReceived: 15.00,
        discountAmount: 0.00
      }

      const result = await SalesTransactionService.create(transactionData)

      expect(result.transactionNumber).toBe('TXN-001')
      expect(result.status).toBe('COMPLETED')
      expect(result.items).toHaveLength(1)
    })
  })

  describe('API Endpoint Integration', () => {
    it('should handle complete product lifecycle', async () => {
      // Create category first
      const mockCategory = {
        id: 1,
        name: 'Test Category',
        description: 'Test description',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockPrisma.category.findFirst.mockResolvedValue(null) // No duplicate
      mockPrisma.category.create.mockResolvedValue(mockCategory)

      const category = await CategoryService.create({
        name: 'Test Category',
        description: 'Test description'
      })

      expect(category.name).toBe('Test Category')

      // Create product
      const mockProduct = {
        id: 1,
        name: 'Test Product',
        categoryId: 1,
        packagingDescription: 'Test package',
        pricePerPackage: 10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1,
        unitPrice: 10.00,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockPrisma.product.findFirst.mockResolvedValue(null) // No duplicate
      mockPrisma.product.create.mockResolvedValue(mockProduct as any)

      const product = await ProductService.create({
        name: 'Test Product',
        categoryId: 1,
        packagingDescription: 'Test package',
        pricePerPackage: 10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1
      })

      expect(product.name).toBe('Test Product')
      expect(product.categoryId).toBe(1)

      // Update product
      const updatedProduct = {
        ...mockProduct,
        name: 'Updated Product',
        updatedAt: new Date()
      }

      mockPrisma.product.findUnique.mockResolvedValue(mockProduct as any)
      mockPrisma.product.findFirst.mockResolvedValue(null) // No name conflict
      mockPrisma.product.update.mockResolvedValue(updatedProduct as any)

      const updated = await ProductService.update(1, {
        name: 'Updated Product'
      })

      expect(updated.name).toBe('Updated Product')

      // Delete product
      mockPrisma.product.delete.mockResolvedValue(mockProduct as any)

      await ProductService.delete(1)

      expect(mockPrisma.product.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      })
    })

    it('should handle inventory management workflow', async () => {
      const mockInventory = {
        id: 1,
        productId: 1,
        currentStock: 100,
        minimumStock: 10,
        maximumStock: 500,
        lastRestockedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Get inventory
      mockPrisma.inventory.findUnique.mockResolvedValue(mockInventory)

      const inventory = await InventoryService.getByProductId(1)

      expect(inventory?.currentStock).toBe(100)

      // Update inventory
      const updatedInventory = {
        ...mockInventory,
        currentStock: 150,
        updatedAt: new Date()
      }

      mockPrisma.inventory.update.mockResolvedValue(updatedInventory)

      const updated = await InventoryService.update(1, {
        currentStock: 150
      })

      expect(updated.currentStock).toBe(150)

      // Stock adjustment
      const stockMovement = {
        id: 1,
        productId: 1,
        movementType: 'IN' as const,
        quantity: 50,
        reason: 'Restocking',
        referenceType: 'PURCHASE' as const,
        referenceId: 123,
        createdAt: new Date()
      }

      mockPrisma.$transaction.mockResolvedValue(stockMovement)

      const movement = await InventoryService.adjustStock(
        1,
        50,
        'Restocking',
        'PURCHASE',
        123
      )

      expect(movement.quantity).toBe(50)
      expect(movement.movementType).toBe('IN')
    })
  })

  describe('Error Handling and Validation', () => {
    it('should handle database connection errors gracefully', async () => {
      mockPrisma.category.findMany.mockRejectedValue(new Error('Database connection failed'))

      await expect(CategoryService.getAll({
        page: 1,
        limit: 20,
        search: '',
        sortBy: 'name',
        sortOrder: 'asc'
      })).rejects.toThrow('Database connection failed')
    })

    it('should validate data integrity constraints', async () => {
      // Test foreign key constraint
      mockPrisma.product.create.mockRejectedValue(
        new Error('Foreign key constraint failed')
      )

      await expect(ProductService.create({
        name: 'Test Product',
        categoryId: 999, // Non-existent category
        packagingDescription: 'Test package',
        pricePerPackage: 10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1
      })).rejects.toThrow('Foreign key constraint failed')
    })

    it('should handle duplicate key violations', async () => {
      mockPrisma.category.create.mockRejectedValue(
        new Error('Unique constraint failed')
      )

      await expect(CategoryService.create({
        name: 'Existing Category'
      })).rejects.toThrow('Unique constraint failed')
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        name: `Product ${i + 1}`,
        categoryId: 1,
        packagingDescription: 'Package',
        pricePerPackage: 10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1,
        unitPrice: 10.00,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        category: { id: 1, name: 'Category' },
        packagingType: { id: 1, name: 'Type' },
        inventory: { currentStock: 100 }
      }))

      mockPrisma.product.findMany.mockResolvedValue(largeDataset as any)
      mockPrisma.product.count.mockResolvedValue(1000)

      const result = await ProductService.getAll({
        page: 1,
        limit: 100,
        search: '',
        sortBy: 'name',
        sortOrder: 'asc'
      })

      expect(result.data).toHaveLength(1000)
      expect(result.pagination.totalItems).toBe(1000)
      expect(result.pagination.totalPages).toBe(10)
    })

    it('should handle concurrent transactions', async () => {
      const mockTransactions = Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        transactionNumber: `TXN-${String(i + 1).padStart(3, '0')}`,
        userId: 1,
        totalAmount: 10.00,
        status: 'COMPLETED',
        createdAt: new Date()
      }))

      mockPrisma.salesTransaction.findMany.mockResolvedValue(mockTransactions as any)
      mockPrisma.salesTransaction.count.mockResolvedValue(10)

      const result = await SalesTransactionService.getAll({
        page: 1,
        limit: 20,
        search: '',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })

      expect(result.data).toHaveLength(10)
      expect(result.data[0].transactionNumber).toBe('TXN-001')
    })
  })
})
