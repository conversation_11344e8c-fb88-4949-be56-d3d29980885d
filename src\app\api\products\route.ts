import { NextRequest, NextResponse } from 'next/server'
import { ProductService } from '@/lib/models/product'
import { createProductSchema, productFiltersSchema } from '@/lib/validations'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const filters = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      search: searchParams.get('search') || '',
      sortBy: searchParams.get('sort_by') || 'name',
      sortOrder: (searchParams.get('sort_order') || 'asc') as 'asc' | 'desc',
      categoryId: searchParams.get('category_id') ? parseInt(searchParams.get('category_id')!) : undefined,
      isActive: searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined,
      minPrice: searchParams.get('min_price') ? parseFloat(searchParams.get('min_price')!) : undefined,
      maxPrice: searchParams.get('max_price') ? parseFloat(searchParams.get('max_price')!) : undefined,
      lowStock: searchParams.get('low_stock') ? searchParams.get('low_stock') === 'true' : undefined
    }

    // Validate filter parameters
    const validatedFilters = productFiltersSchema.parse(filters)
    
    const result = await ProductService.getAll(validatedFilters)
    
    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching products:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid parameters',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch products'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = createProductSchema.parse(body)
    
    // Check if SKU already exists
    if (validatedData.sku) {
      const skuExists = await ProductService.skuExists(validatedData.sku)
      if (skuExists) {
        return NextResponse.json({
          success: false,
          error: 'SKU already exists'
        }, { status: 409 })
      }
    }
    
    // Check if barcode already exists
    if (validatedData.barcode) {
      const barcodeExists = await ProductService.barcodeExists(validatedData.barcode)
      if (barcodeExists) {
        return NextResponse.json({
          success: false,
          error: 'Barcode already exists'
        }, { status: 409 })
      }
    }
    
    const product = await ProductService.create(validatedData)
    
    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating product:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create product'
    }, { status: 500 })
  }
}
