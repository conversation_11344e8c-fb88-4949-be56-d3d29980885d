import { NextApiRequest, NextApiResponse } from 'next';
import { ProductModel } from '../../../models/Product';
import { ApiResponse, CreateProductRequest, ProductFilters } from '../../../types';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res);
        break;
      case 'POST':
        await handlePost(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).json({
          success: false,
          error: `Method ${req.method} Not Allowed`
        });
    }
  } catch (error) {
    console.error('Products API error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
  const {
    page = '1',
    limit = '20',
    search = '',
    sort_by = 'name',
    sort_order = 'asc',
    category_id,
    is_active,
    min_price,
    max_price,
    low_stock
  } = req.query;

  const filters: ProductFilters = {
    page: parseInt(page as string),
    limit: parseInt(limit as string),
    search: search as string,
    sort_by: sort_by as string,
    sort_order: sort_order as 'asc' | 'desc'
  };

  if (category_id) filters.category_id = parseInt(category_id as string);
  if (is_active !== undefined) filters.is_active = is_active === 'true';
  if (min_price) filters.min_price = parseFloat(min_price as string);
  if (max_price) filters.max_price = parseFloat(max_price as string);
  if (low_stock) filters.low_stock = low_stock === 'true';

  try {
    const result = await ProductModel.getAll(filters);
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products'
    });
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
  const {
    name,
    category_id,
    packaging_description,
    price_per_package,
    units_per_package,
    packaging_type_id,
    sku,
    barcode,
    description
  }: CreateProductRequest = req.body;

  // Validation
  if (!name || name.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Product name is required'
    });
  }

  if (!category_id || isNaN(category_id)) {
    return res.status(400).json({
      success: false,
      error: 'Valid category ID is required'
    });
  }

  if (!packaging_description || packaging_description.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Packaging description is required'
    });
  }

  if (!price_per_package || price_per_package <= 0) {
    return res.status(400).json({
      success: false,
      error: 'Valid price per package is required'
    });
  }

  if (!units_per_package || units_per_package <= 0) {
    return res.status(400).json({
      success: false,
      error: 'Valid units per package is required'
    });
  }

  if (!packaging_type_id || isNaN(packaging_type_id)) {
    return res.status(400).json({
      success: false,
      error: 'Valid packaging type ID is required'
    });
  }

  try {
    // Check if SKU already exists
    if (sku && sku.trim().length > 0) {
      const skuExists = await ProductModel.skuExists(sku.trim());
      if (skuExists) {
        return res.status(409).json({
          success: false,
          error: 'SKU already exists'
        });
      }
    }

    // Check if barcode already exists
    if (barcode && barcode.trim().length > 0) {
      const barcodeExists = await ProductModel.barcodeExists(barcode.trim());
      if (barcodeExists) {
        return res.status(409).json({
          success: false,
          error: 'Barcode already exists'
        });
      }
    }

    const product = await ProductModel.create({
      name: name.trim(),
      category_id,
      packaging_description: packaging_description.trim(),
      price_per_package,
      units_per_package,
      packaging_type_id,
      sku: sku?.trim(),
      barcode: barcode?.trim(),
      description: description?.trim()
    });

    res.status(201).json({
      success: true,
      data: product,
      message: 'Product created successfully'
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create product'
    });
  }
}
