import { prisma } from "@/lib/prisma";
import type {
  CreateProductInput,
  ProductFiltersInput,
  UpdateProductInput,
} from "@/lib/validations";
import { Prisma } from "@prisma/client";

export class ProductService {
  // Get all products with filters and pagination
  static async getAll(filters: ProductFiltersInput = {}) {
    const {
      page = 1,
      limit = 20,
      search = "",
      sortBy = "name",
      sortOrder = "asc",
      categoryId,
      isActive,
      minPrice,
      maxPrice,
      lowStock,
    } = filters;

    const offset = (page - 1) * limit;

    const where: Prisma.ProductWhereInput = {};

    // Search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // Category filter
    if (categoryId) {
      where.categoryId = categoryId;
    }

    // Active status filter
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    // Price range filters
    if (minPrice !== undefined || maxPrice !== undefined) {
      where.pricePerPackage = {};
      if (minPrice !== undefined) where.pricePerPackage.gte = minPrice;
      if (maxPrice !== undefined) where.pricePerPackage.lte = maxPrice;
    }

    // Low stock filter - will be handled in a separate query if needed
    if (lowStock) {
      // This requires a more complex query, will be implemented separately
    }

    const [products, totalItems] = await Promise.all([
      prisma.product.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip: offset,
        take: limit,
        include: {
          category: { select: { name: true } },
          packagingType: { select: { name: true } },
          inventory: { select: { currentStock: true } },
        },
      }),
      prisma.product.count({ where }),
    ]);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: products.map((product) => ({
        ...product,
        categoryName: product.category.name,
        packagingTypeName: product.packagingType.name,
        currentStock: product.inventory?.currentStock || 0,
        unitPrice: product.pricePerPackage.toNumber() / product.unitsPerPackage,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  // Get product by ID with related data
  static async getById(id: number) {
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: { select: { name: true } },
        packagingType: { select: { name: true } },
        inventory: {
          select: {
            currentStock: true,
            minimumStock: true,
            maximumStock: true,
          },
        },
      },
    });

    if (!product) return null;

    return {
      ...product,
      categoryName: product.category.name,
      packagingTypeName: product.packagingType.name,
      currentStock: product.inventory?.currentStock || 0,
      unitPrice: product.pricePerPackage.toNumber() / product.unitsPerPackage,
    };
  }

  // Get product by SKU
  static async getBySku(sku: string) {
    return await prisma.product.findUnique({
      where: { sku },
    });
  }

  // Get product by barcode
  static async getByBarcode(barcode: string) {
    return await prisma.product.findUnique({
      where: { barcode },
    });
  }

  // Create new product
  static async create(data: CreateProductInput) {
    return await prisma.$transaction(async (tx) => {
      // Calculate unit price
      const unitPrice = data.pricePerPackage / data.unitsPerPackage;

      const product = await tx.product.create({
        data: {
          name: data.name,
          categoryId: data.categoryId,
          packagingDescription: data.packagingDescription,
          pricePerPackage: data.pricePerPackage,
          unitsPerPackage: data.unitsPerPackage,
          packagingTypeId: data.packagingTypeId,
          unitPrice: unitPrice,
          sku: data.sku || null,
          barcode: data.barcode || null,
          description: data.description || null,
        },
      });

      // Initialize inventory for the new product
      await tx.inventory.create({
        data: {
          productId: product.id,
          currentStock: 0,
          minimumStock: 10,
          maximumStock: 200,
        },
      });

      return product;
    });
  }

  // Update product
  static async update(id: number, data: UpdateProductInput) {
    const updateData: Prisma.ProductUpdateInput = {};

    if (data.name !== undefined) updateData.name = data.name;
    if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
    if (data.packagingDescription !== undefined)
      updateData.packagingDescription = data.packagingDescription;
    if (data.pricePerPackage !== undefined)
      updateData.pricePerPackage = data.pricePerPackage;
    if (data.unitsPerPackage !== undefined)
      updateData.unitsPerPackage = data.unitsPerPackage;
    if (data.packagingTypeId !== undefined)
      updateData.packagingTypeId = data.packagingTypeId;
    if (data.sku !== undefined) updateData.sku = data.sku;
    if (data.barcode !== undefined) updateData.barcode = data.barcode;
    if (data.description !== undefined)
      updateData.description = data.description;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    // Recalculate unit price if price or units changed
    if (
      data.pricePerPackage !== undefined ||
      data.unitsPerPackage !== undefined
    ) {
      const currentProduct = await prisma.product.findUnique({ where: { id } });
      if (currentProduct) {
        const newPrice =
          data.pricePerPackage ?? currentProduct.pricePerPackage.toNumber();
        const newUnits = data.unitsPerPackage ?? currentProduct.unitsPerPackage;
        updateData.unitPrice = newPrice / newUnits;
      }
    }

    return await prisma.product.update({
      where: { id },
      data: updateData,
    });
  }

  // Delete product (soft delete by setting isActive to false)
  static async delete(id: number) {
    await prisma.product.update({
      where: { id },
      data: { isActive: false },
    });
    return true;
  }

  // Hard delete product (use with caution)
  static async hardDelete(id: number) {
    // Check if product has been sold
    const salesCount = await prisma.salesTransactionItem.count({
      where: { productId: id },
    });

    if (salesCount > 0) {
      throw new Error("Cannot delete product that has sales history");
    }

    await prisma.product.delete({
      where: { id },
    });
    return true;
  }

  // Get products with low stock
  static async getLowStockProducts() {
    return await prisma.product.findMany({
      where: {
        isActive: true,
        inventory: {
          currentStock: {
            lte: prisma.inventory.fields.minimumStock,
          },
        },
      },
      include: {
        inventory: {
          select: { currentStock: true, minimumStock: true },
        },
      },
      orderBy: {
        inventory: {
          currentStock: "asc",
        },
      },
    });
  }

  // Get top selling products
  static async getTopSellingProducts(limit: number = 10) {
    const products = await prisma.product.findMany({
      where: { isActive: true },
      include: {
        transactionItems: {
          include: {
            transaction: {
              where: { status: "COMPLETED" },
            },
          },
        },
      },
    });

    const productSales = products.map((product) => {
      const totalSold = product.transactionItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const revenue = product.transactionItems.reduce(
        (sum, item) => sum + item.totalPrice.toNumber(),
        0
      );

      return {
        ...product,
        totalSold,
        revenue,
      };
    });

    return productSales
      .filter((p) => p.totalSold > 0)
      .sort((a, b) => b.totalSold - a.totalSold)
      .slice(0, limit);
  }

  // Check if SKU exists (for validation)
  static async skuExists(sku: string, excludeId?: number) {
    const where: Prisma.ProductWhereInput = { sku };
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const product = await prisma.product.findFirst({ where });
    return !!product;
  }

  // Check if barcode exists (for validation)
  static async barcodeExists(barcode: string, excludeId?: number) {
    const where: Prisma.ProductWhereInput = { barcode };
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const product = await prisma.product.findFirst({ where });
    return !!product;
  }

  // Get product statistics
  static async getStatistics() {
    const [
      totalProducts,
      activeProducts,
      lowStockProducts,
      outOfStockProducts,
      inventoryValue,
    ] = await Promise.all([
      prisma.product.count(),
      prisma.product.count({ where: { isActive: true } }),
      prisma.product.count({
        where: {
          isActive: true,
          inventory: {
            AND: [
              { currentStock: { lte: prisma.inventory.fields.minimumStock } },
              { currentStock: { gt: 0 } },
            ],
          },
        },
      }),
      prisma.product.count({
        where: {
          isActive: true,
          inventory: { currentStock: 0 },
        },
      }),
      prisma.product.aggregate({
        where: { isActive: true },
        _sum: {
          pricePerPackage: true,
        },
      }),
    ]);

    return {
      totalProducts,
      activeProducts,
      lowStockProducts,
      outOfStockProducts,
      totalInventoryValue: inventoryValue._sum.pricePerPackage?.toNumber() || 0,
    };
  }
}
