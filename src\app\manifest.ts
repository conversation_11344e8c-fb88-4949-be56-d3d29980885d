import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'POS Arta - Point of Sale System',
    short_name: 'POS Arta',
    description: 'Modern Point of Sale application built with Next.js, Prisma, and shadcn/ui',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    icons: [
      {
        src: '/favicon.ico',
        sizes: 'any',
        type: 'image/x-icon',
      },
    ],
    categories: ['business', 'productivity', 'retail'],
    orientation: 'portrait-primary',
    scope: '/',
    lang: 'en',
  }
}
