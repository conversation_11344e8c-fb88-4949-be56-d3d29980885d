#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`
}

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(colorize(`✅ ${description}`, 'green'))
    return true
  } else {
    console.log(colorize(`❌ ${description} - MISSING: ${filePath}`, 'red'))
    return false
  }
}

function validateDirectoryStructure() {
  console.log(colorize('\n📁 Validating Directory Structure...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const requiredDirs = [
    { path: 'app', description: 'Next.js App Router directory' },
    { path: 'app/api', description: 'API routes directory' },
    { path: 'app/(dashboard)', description: 'Dashboard route group' },
    { path: 'components', description: 'React components directory' },
    { path: 'components/ui', description: 'shadcn/ui components directory' },
    { path: 'components/pos', description: 'POS-specific components directory' },
    { path: 'lib', description: 'Library utilities directory' },
    { path: 'lib/models', description: 'Prisma service models directory' },
    { path: 'hooks', description: 'React hooks directory' },
    { path: 'utils', description: 'Utility functions directory' },
    { path: '__tests__', description: 'Test files directory' },
    { path: 'prisma', description: 'Prisma configuration directory' }
  ]
  
  let allDirsExist = true
  
  requiredDirs.forEach(({ path: dirPath, description }) => {
    if (!checkFileExists(dirPath, description)) {
      allDirsExist = false
    }
  })
  
  return allDirsExist
}

function validateCoreFiles() {
  console.log(colorize('\n📄 Validating Core Files...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const coreFiles = [
    { path: 'package.json', description: 'Package configuration' },
    { path: 'next.config.js', description: 'Next.js configuration' },
    { path: 'tailwind.config.ts', description: 'Tailwind CSS configuration' },
    { path: 'tsconfig.json', description: 'TypeScript configuration' },
    { path: 'jest.config.js', description: 'Jest test configuration' },
    { path: 'jest.setup.js', description: 'Jest setup file' },
    { path: 'middleware.ts', description: 'Next.js middleware' },
    { path: 'app/layout.tsx', description: 'Root layout component' },
    { path: 'app/page.tsx', description: 'Home page component' },
    { path: 'app/globals.css', description: 'Global CSS styles' },
    { path: 'app/not-found.tsx', description: '404 page component' },
    { path: 'app/global-error.tsx', description: 'Global error boundary' }
  ]
  
  let allFilesExist = true
  
  coreFiles.forEach(({ path: filePath, description }) => {
    if (!checkFileExists(filePath, description)) {
      allFilesExist = false
    }
  })
  
  return allFilesExist
}

function validatePrismaSetup() {
  console.log(colorize('\n🗄️  Validating Prisma Setup...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const prismaFiles = [
    { path: 'prisma/schema.prisma', description: 'Prisma schema definition' },
    { path: 'lib/prisma.ts', description: 'Prisma client configuration' },
    { path: 'lib/models/category.ts', description: 'Category service model' },
    { path: 'lib/models/product.ts', description: 'Product service model' },
    { path: 'lib/models/sales-transaction.ts', description: 'Sales transaction service model' },
    { path: 'lib/models/inventory.ts', description: 'Inventory service model' },
    { path: 'lib/validations.ts', description: 'Zod validation schemas' }
  ]
  
  let allPrismaFilesExist = true
  
  prismaFiles.forEach(({ path: filePath, description }) => {
    if (!checkFileExists(filePath, description)) {
      allPrismaFilesExist = false
    }
  })
  
  return allPrismaFilesExist
}

function validateAPIRoutes() {
  console.log(colorize('\n🌐 Validating API Routes...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const apiRoutes = [
    { path: 'app/api/categories/route.ts', description: 'Categories API endpoint' },
    { path: 'app/api/categories/[id]/route.ts', description: 'Category by ID API endpoint' },
    { path: 'app/api/products/route.ts', description: 'Products API endpoint' },
    { path: 'app/api/products/[id]/route.ts', description: 'Product by ID API endpoint' },
    { path: 'app/api/sales/route.ts', description: 'Sales API endpoint' },
    { path: 'app/api/sales/[id]/route.ts', description: 'Sales by ID API endpoint' },
    { path: 'app/api/inventory/route.ts', description: 'Inventory API endpoint' },
    { path: 'app/api/inventory/[productId]/route.ts', description: 'Inventory by product API endpoint' },
    { path: 'app/api/analytics/route.ts', description: 'Analytics API endpoint' },
    { path: 'app/api/health/route.ts', description: 'Health check API endpoint' },
    { path: 'app/api/docs/route.ts', description: 'API documentation endpoint' }
  ]
  
  let allAPIRoutesExist = true
  
  apiRoutes.forEach(({ path: filePath, description }) => {
    if (!checkFileExists(filePath, description)) {
      allAPIRoutesExist = false
    }
  })
  
  return allAPIRoutesExist
}

function validateUIComponents() {
  console.log(colorize('\n🎨 Validating UI Components...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const uiComponents = [
    // shadcn/ui components
    { path: 'components/ui/button.tsx', description: 'Button component' },
    { path: 'components/ui/input.tsx', description: 'Input component' },
    { path: 'components/ui/card.tsx', description: 'Card component' },
    { path: 'components/ui/dialog.tsx', description: 'Dialog component' },
    { path: 'components/ui/label.tsx', description: 'Label component' },
    { path: 'components/ui/textarea.tsx', description: 'Textarea component' },
    { path: 'components/ui/badge.tsx', description: 'Badge component' },
    { path: 'components/ui/select.tsx', description: 'Select component' },
    { path: 'components/ui/table.tsx', description: 'Table component' },
    { path: 'components/ui/separator.tsx', description: 'Separator component' },
    { path: 'components/ui/skeleton.tsx', description: 'Skeleton component' },
    { path: 'components/ui/toast.tsx', description: 'Toast component' },
    { path: 'components/ui/toaster.tsx', description: 'Toaster component' },
    
    // POS components
    { path: 'components/pos/product-grid.tsx', description: 'Product grid component' },
    { path: 'components/pos/cart.tsx', description: 'Shopping cart component' },
    { path: 'components/pos/checkout-dialog.tsx', description: 'Checkout dialog component' },
    { path: 'components/pos/receipt-dialog.tsx', description: 'Receipt dialog component' },
    { path: 'components/pos/category-sidebar.tsx', description: 'Category sidebar component' },
    
    // Utility components
    { path: 'lib/utils.ts', description: 'Utility functions' },
    { path: 'hooks/use-toast.ts', description: 'Toast hook' }
  ]
  
  let allComponentsExist = true
  
  uiComponents.forEach(({ path: filePath, description }) => {
    if (!checkFileExists(filePath, description)) {
      allComponentsExist = false
    }
  })
  
  return allComponentsExist
}

function validateTestFiles() {
  console.log(colorize('\n🧪 Validating Test Files...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const testFiles = [
    { path: '__tests__/api/categories.test.js', description: 'Categories API tests (existing)' },
    { path: '__tests__/api/products.test.ts', description: 'Products API tests' },
    { path: '__tests__/api/sales.test.ts', description: 'Sales API tests' },
    { path: '__tests__/api/inventory.test.ts', description: 'Inventory API tests' },
    { path: '__tests__/components/pos/product-grid.test.tsx', description: 'Product grid component tests' },
    { path: '__tests__/components/pos/cart.test.tsx', description: 'Cart component tests' },
    { path: '__tests__/lib/validations.test.ts', description: 'Validation schema tests' },
    { path: '__tests__/integration/migration.test.ts', description: 'Migration integration tests' },
    { path: 'scripts/test-migration.js', description: 'Migration test runner script' }
  ]
  
  let allTestFilesExist = true
  
  testFiles.forEach(({ path: filePath, description }) => {
    if (!checkFileExists(filePath, description)) {
      allTestFilesExist = false
    }
  })
  
  return allTestFilesExist
}

function validateAppRouterPages() {
  console.log(colorize('\n📱 Validating App Router Pages...', 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  const appPages = [
    { path: 'app/(dashboard)/pos/page.tsx', description: 'POS page' },
    { path: 'app/(dashboard)/pos/layout.tsx', description: 'POS layout' },
    { path: 'app/(dashboard)/pos/loading.tsx', description: 'POS loading component' },
    { path: 'app/(dashboard)/pos/error.tsx', description: 'POS error boundary' },
    { path: 'app/(dashboard)/sales/page.tsx', description: 'Sales page' },
    { path: 'app/(dashboard)/sales/layout.tsx', description: 'Sales layout' },
    { path: 'app/(dashboard)/sales/loading.tsx', description: 'Sales loading component' },
    { path: 'app/(dashboard)/sales/error.tsx', description: 'Sales error boundary' },
    { path: 'app/(dashboard)/layout.tsx', description: 'Dashboard layout' },
    { path: 'app/template.tsx', description: 'Page template with animations' },
    { path: 'app/robots.ts', description: 'Robots.txt generator' },
    { path: 'app/sitemap.ts', description: 'Sitemap generator' },
    { path: 'app/manifest.ts', description: 'PWA manifest generator' },
    { path: 'app/opengraph-image.tsx', description: 'OpenGraph image generator' }
  ]
  
  let allPagesExist = true
  
  appPages.forEach(({ path: filePath, description }) => {
    if (!checkFileExists(filePath, description)) {
      allPagesExist = false
    }
  })
  
  return allPagesExist
}

function generateValidationReport(results) {
  console.log(colorize('\n📊 Migration Validation Report', 'magenta'))
  console.log(colorize('═'.repeat(60), 'magenta'))
  
  const categories = [
    { name: 'Directory Structure', result: results.directories },
    { name: 'Core Files', result: results.coreFiles },
    { name: 'Prisma Setup', result: results.prisma },
    { name: 'API Routes', result: results.apiRoutes },
    { name: 'UI Components', result: results.uiComponents },
    { name: 'Test Files', result: results.testFiles },
    { name: 'App Router Pages', result: results.appPages }
  ]
  
  let totalPassed = 0
  let totalFailed = 0
  
  categories.forEach(({ name, result }) => {
    const status = result ? 
      colorize('✅ VALID', 'green') : 
      colorize('❌ INVALID', 'red')
    
    console.log(`${name}: ${status}`)
    
    if (result) {
      totalPassed++
    } else {
      totalFailed++
    }
  })
  
  console.log(colorize('\n📈 Overall Validation Results:', 'bright'))
  console.log(`Total Categories: ${totalPassed + totalFailed}`)
  console.log(`Valid: ${colorize(totalPassed, 'green')}`)
  console.log(`Invalid: ${colorize(totalFailed, 'red')}`)
  
  const overallStatus = totalFailed === 0 ? 'VALID' : 'INVALID'
  const statusColor = totalFailed === 0 ? 'green' : 'red'
  
  console.log(`\nMigration Status: ${colorize(overallStatus, statusColor)}`)
  
  if (totalFailed === 0) {
    console.log(colorize('\n🎉 Migration validation successful!', 'green'))
    console.log(colorize('✅ All required files and components are present.', 'green'))
    console.log(colorize('🚀 POS Arta is ready for testing and deployment.', 'green'))
  } else {
    console.log(colorize('\n⚠️  Migration validation failed!', 'red'))
    console.log(colorize('❌ Some required files or components are missing.', 'red'))
    console.log(colorize('🔧 Please review the missing items above and complete the migration.', 'yellow'))
  }
  
  return totalFailed === 0
}

function main() {
  console.log(colorize('POS Arta - Migration Validation', 'bright'))
  console.log(colorize('Modern Stack Migration Checker', 'cyan'))
  console.log(colorize('═'.repeat(60), 'cyan'))
  
  const results = {
    directories: validateDirectoryStructure(),
    coreFiles: validateCoreFiles(),
    prisma: validatePrismaSetup(),
    apiRoutes: validateAPIRoutes(),
    uiComponents: validateUIComponents(),
    testFiles: validateTestFiles(),
    appPages: validateAppRouterPages()
  }
  
  const isValid = generateValidationReport(results)
  
  process.exit(isValid ? 0 : 1)
}

// Run validation
main()
