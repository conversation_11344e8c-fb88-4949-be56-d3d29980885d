import { db } from '../lib/database';
import {
  SalesTransaction,
  SalesTransactionItem,
  CreateSalesTransactionRequest,
  TransactionFilters,
  PaginatedResponse
} from '../types';
import { generateTransactionNumber } from '../utils/format';

export class SalesTransactionModel {
  // Create a new sales transaction with items
  static async create(
    data: CreateSalesTransactionRequest,
    userId: number
  ): Promise<SalesTransaction & { items: SalesTransactionItem[] }> {
    return await db.transaction(async (client) => {
      // Calculate totals
      const subtotal = data.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
      const discountAmount = data.discount_amount || 0;
      const taxAmount = (subtotal - discountAmount) * 0.10; // 10% tax
      const finalAmount = subtotal - discountAmount + taxAmount;
      const changeAmount = data.payment_received - finalAmount;

      if (changeAmount < 0) {
        throw new Error('Insufficient payment amount');
      }

      // Generate transaction number
      const transactionNumber = generateTransactionNumber();

      // Create transaction
      const transactionQuery = `
        INSERT INTO sales_transactions (
          transaction_number, user_id, total_amount, tax_amount, 
          discount_amount, final_amount, payment_method, 
          payment_received, change_amount, notes
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `;

      const transactionResult = await client.query(transactionQuery, [
        transactionNumber,
        userId,
        subtotal,
        taxAmount,
        discountAmount,
        finalAmount,
        data.payment_method,
        data.payment_received,
        changeAmount,
        data.notes
      ]);

      const transaction = transactionResult.rows[0];

      // Create transaction items and update inventory
      const items: SalesTransactionItem[] = [];
      
      for (const item of data.items) {
        // Check stock availability
        const stockCheck = await client.query(
          'SELECT current_stock FROM inventory WHERE product_id = $1',
          [item.product_id]
        );

        if (!stockCheck.rows[0] || stockCheck.rows[0].current_stock < item.quantity) {
          throw new Error(`Insufficient stock for product ID ${item.product_id}`);
        }

        // Insert transaction item
        const itemQuery = `
          INSERT INTO sales_transaction_items (
            transaction_id, product_id, quantity, unit_price, total_price
          )
          VALUES ($1, $2, $3, $4, $5)
          RETURNING *
        `;

        const totalPrice = item.quantity * item.unit_price;
        const itemResult = await client.query(itemQuery, [
          transaction.id,
          item.product_id,
          item.quantity,
          item.unit_price,
          totalPrice
        ]);

        items.push(itemResult.rows[0]);

        // Update inventory (this will trigger the automatic stock movement via trigger)
        await client.query(
          'UPDATE inventory SET current_stock = current_stock - $1 WHERE product_id = $2',
          [item.quantity, item.product_id]
        );
      }

      return { ...transaction, items };
    });
  }

  // Get transaction by ID with items
  static async getById(id: number): Promise<(SalesTransaction & { items: SalesTransactionItem[] }) | null> {
    const transactionQuery = `
      SELECT 
        st.*,
        u.full_name as user_name
      FROM sales_transactions st
      LEFT JOIN users u ON st.user_id = u.id
      WHERE st.id = $1
    `;

    const itemsQuery = `
      SELECT 
        sti.*,
        p.name as product_name,
        p.sku as product_sku
      FROM sales_transaction_items sti
      LEFT JOIN products p ON sti.product_id = p.id
      WHERE sti.transaction_id = $1
      ORDER BY sti.id
    `;

    const [transactionResult, itemsResult] = await Promise.all([
      db.query(transactionQuery, [id]),
      db.query(itemsQuery, [id])
    ]);

    if (transactionResult.rows.length === 0) {
      return null;
    }

    return {
      ...transactionResult.rows[0],
      items: itemsResult.rows
    };
  }

  // Get transaction by transaction number
  static async getByTransactionNumber(transactionNumber: string): Promise<SalesTransaction | null> {
    const query = `
      SELECT 
        st.*,
        u.full_name as user_name
      FROM sales_transactions st
      LEFT JOIN users u ON st.user_id = u.id
      WHERE st.transaction_number = $1
    `;

    const result = await db.query(query, [transactionNumber]);
    return result.rows[0] || null;
  }

  // Get all transactions with filters and pagination
  static async getAll(filters?: TransactionFilters): Promise<PaginatedResponse<SalesTransaction>> {
    const page = filters?.page || 1;
    const limit = filters?.limit || 20;
    const offset = (page - 1) * limit;
    const search = filters?.search || '';
    const sortBy = filters?.sort_by || 'created_at';
    const sortOrder = filters?.sort_order || 'desc';

    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Search filter
    if (search) {
      whereConditions.push(`(st.transaction_number ILIKE $${paramIndex} OR u.full_name ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Date range filters
    if (filters?.start_date) {
      whereConditions.push(`st.created_at >= $${paramIndex}`);
      queryParams.push(filters.start_date);
      paramIndex++;
    }

    if (filters?.end_date) {
      whereConditions.push(`st.created_at <= $${paramIndex}`);
      queryParams.push(filters.end_date);
      paramIndex++;
    }

    // User filter
    if (filters?.user_id) {
      whereConditions.push(`st.user_id = $${paramIndex}`);
      queryParams.push(filters.user_id);
      paramIndex++;
    }

    // Payment method filter
    if (filters?.payment_method) {
      whereConditions.push(`st.payment_method = $${paramIndex}`);
      queryParams.push(filters.payment_method);
      paramIndex++;
    }

    // Status filter
    if (filters?.status) {
      whereConditions.push(`st.status = $${paramIndex}`);
      queryParams.push(filters.status);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Base query with joins
    const baseQuery = `
      FROM sales_transactions st
      LEFT JOIN users u ON st.user_id = u.id
      ${whereClause}
    `;

    // Get total count
    const countQuery = `SELECT COUNT(*) ${baseQuery}`;
    const countResult = await db.query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT 
        st.*,
        u.full_name as user_name
      ${baseQuery}
      ORDER BY st.${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(limit, offset);

    const dataResult = await db.query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: dataResult.rows,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: totalItems,
        items_per_page: limit,
        has_next: page < totalPages,
        has_prev: page > 1
      }
    };
  }

  // Update transaction status
  static async updateStatus(id: number, status: 'pending' | 'completed' | 'cancelled' | 'refunded'): Promise<SalesTransaction | null> {
    const query = `
      UPDATE sales_transactions 
      SET status = $1
      WHERE id = $2
      RETURNING *
    `;

    const result = await db.query(query, [status, id]);
    return result.rows[0] || null;
  }

  // Get sales analytics
  static async getSalesAnalytics(startDate?: string, endDate?: string): Promise<{
    total_sales: number;
    total_transactions: number;
    average_transaction: number;
    top_products: Array<{
      product_name: string;
      quantity_sold: number;
      revenue: number;
    }>;
    sales_by_category: Array<{
      category_name: string;
      total_sales: number;
      percentage: number;
    }>;
    daily_sales: Array<{
      date: string;
      sales: number;
      transactions: number;
    }>;
  }> {
    let dateFilter = '';
    const params: any[] = [];
    let paramIndex = 1;

    if (startDate && endDate) {
      dateFilter = `WHERE st.created_at >= $${paramIndex} AND st.created_at <= $${paramIndex + 1} AND st.status = 'completed'`;
      params.push(startDate, endDate);
      paramIndex += 2;
    } else {
      dateFilter = `WHERE st.status = 'completed'`;
    }

    // Basic sales metrics
    const metricsQuery = `
      SELECT 
        COALESCE(SUM(final_amount), 0) as total_sales,
        COUNT(*) as total_transactions,
        COALESCE(AVG(final_amount), 0) as average_transaction
      FROM sales_transactions st
      ${dateFilter}
    `;

    // Top products
    const topProductsQuery = `
      SELECT 
        p.name as product_name,
        SUM(sti.quantity) as quantity_sold,
        SUM(sti.total_price) as revenue
      FROM sales_transaction_items sti
      JOIN products p ON sti.product_id = p.id
      JOIN sales_transactions st ON sti.transaction_id = st.id
      ${dateFilter}
      GROUP BY p.id, p.name
      ORDER BY revenue DESC
      LIMIT 10
    `;

    // Sales by category
    const categoryQuery = `
      SELECT 
        c.name as category_name,
        SUM(sti.total_price) as total_sales
      FROM sales_transaction_items sti
      JOIN products p ON sti.product_id = p.id
      JOIN categories c ON p.category_id = c.id
      JOIN sales_transactions st ON sti.transaction_id = st.id
      ${dateFilter}
      GROUP BY c.id, c.name
      ORDER BY total_sales DESC
    `;

    // Daily sales (last 30 days)
    const dailySalesQuery = `
      SELECT 
        DATE(st.created_at) as date,
        SUM(st.final_amount) as sales,
        COUNT(*) as transactions
      FROM sales_transactions st
      WHERE st.created_at >= CURRENT_DATE - INTERVAL '30 days' 
        AND st.status = 'completed'
      GROUP BY DATE(st.created_at)
      ORDER BY date DESC
    `;

    const [metricsResult, topProductsResult, categoryResult, dailySalesResult] = await Promise.all([
      db.query(metricsQuery, params),
      db.query(topProductsQuery, params),
      db.query(categoryQuery, params),
      db.query(dailySalesQuery)
    ]);

    const metrics = metricsResult.rows[0];
    const totalCategorySales = categoryResult.rows.reduce((sum, row) => sum + parseFloat(row.total_sales), 0);

    return {
      total_sales: parseFloat(metrics.total_sales),
      total_transactions: parseInt(metrics.total_transactions),
      average_transaction: parseFloat(metrics.average_transaction),
      top_products: topProductsResult.rows.map(row => ({
        product_name: row.product_name,
        quantity_sold: parseInt(row.quantity_sold),
        revenue: parseFloat(row.revenue)
      })),
      sales_by_category: categoryResult.rows.map(row => ({
        category_name: row.category_name,
        total_sales: parseFloat(row.total_sales),
        percentage: totalCategorySales > 0 ? (parseFloat(row.total_sales) / totalCategorySales) * 100 : 0
      })),
      daily_sales: dailySalesResult.rows.map(row => ({
        date: row.date,
        sales: parseFloat(row.sales),
        transactions: parseInt(row.transactions)
      }))
    };
  }
}
