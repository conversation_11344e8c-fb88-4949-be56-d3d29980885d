import { SalesTransactionService } from "@/lib/models/sales-transaction";
import {
  createSalesTransactionSchema,
  transactionFiltersSchema,
} from "@/lib/validations";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Set runtime to nodejs for better performance
export const runtime = "nodejs";

// Enable CORS for this route
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

export async function OPTIONS() {
  return new NextResponse(null, { status: 200, headers: corsHeaders });
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const filters = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "20"),
      search: searchParams.get("search") || "",
      sortBy: searchParams.get("sort_by") || "createdAt",
      sortOrder: (searchParams.get("sort_order") || "desc") as "asc" | "desc",
      startDate: searchParams.get("start_date") || undefined,
      endDate: searchParams.get("end_date") || undefined,
      userId: searchParams.get("user_id")
        ? parseInt(searchParams.get("user_id")!)
        : undefined,
      paymentMethod: searchParams.get("payment_method") as
        | "CASH"
        | "CARD"
        | "DIGITAL"
        | undefined,
      status: searchParams.get("status") as
        | "PENDING"
        | "COMPLETED"
        | "CANCELLED"
        | "REFUNDED"
        | undefined,
    };

    // Validate filter parameters
    const validatedFilters = transactionFiltersSchema.parse(filters);

    const result = await SalesTransactionService.getAll(validatedFilters);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch transactions",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = createSalesTransactionSchema.parse(body);

    // TODO: Get actual user ID from authentication
    // For now, using default cashier user ID (2)
    const userId = 2;

    const transaction = await SalesTransactionService.create(
      validatedData,
      userId
    );

    return NextResponse.json(
      {
        success: true,
        data: transaction,
        message: "Transaction created successfully",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating transaction:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation failed",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("Insufficient stock")) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
          },
          { status: 409 }
        );
      }

      if (error.message.includes("Insufficient payment")) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: "Failed to create transaction",
      },
      { status: 500 }
    );
  }
}
