import { prisma } from '@/lib/prisma'
import { Prisma } from '@prisma/client'
import type { CreateCategoryInput, UpdateCategoryInput, PaginationInput } from '@/lib/validations'

export class CategoryService {
  // Get all categories with optional pagination
  static async getAll(params: PaginationInput = {}) {
    const { page = 1, limit = 50, search = '', sortBy = 'name', sortOrder = 'asc' } = params
    const offset = (page - 1) * limit

    const where: Prisma.CategoryWhereInput = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }
      : {}

    const [categories, totalItems] = await Promise.all([
      prisma.category.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip: offset,
        take: limit,
        include: {
          _count: {
            select: { products: true }
          }
        }
      }),
      prisma.category.count({ where })
    ])

    const totalPages = Math.ceil(totalItems / limit)

    return {
      data: categories.map(category => ({
        ...category,
        productCount: category._count.products
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }

  // Get category by ID
  static async getById(id: number) {
    return await prisma.category.findUnique({
      where: { id },
      include: {
        _count: {
          select: { products: true }
        }
      }
    })
  }

  // Get category by name
  static async getByName(name: string) {
    return await prisma.category.findUnique({
      where: { name }
    })
  }

  // Create new category
  static async create(data: CreateCategoryInput) {
    return await prisma.category.create({
      data: {
        name: data.name,
        description: data.description || null
      }
    })
  }

  // Update category
  static async update(id: number, data: UpdateCategoryInput) {
    const updateData: Prisma.CategoryUpdateInput = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description

    return await prisma.category.update({
      where: { id },
      data: updateData
    })
  }

  // Delete category (only if no products)
  static async delete(id: number) {
    // Check if category has products
    const productCount = await prisma.product.count({
      where: { categoryId: id }
    })

    if (productCount > 0) {
      throw new Error('Cannot delete category that has products')
    }

    await prisma.category.delete({
      where: { id }
    })

    return true
  }

  // Get categories with product count
  static async getCategoriesWithProductCount() {
    return await prisma.category.findMany({
      include: {
        _count: {
          select: { products: { where: { isActive: true } } }
        }
      },
      orderBy: { name: 'asc' }
    })
  }

  // Check if category name exists (for validation)
  static async nameExists(name: string, excludeId?: number) {
    const where: Prisma.CategoryWhereInput = { name }
    if (excludeId) {
      where.id = { not: excludeId }
    }

    const category = await prisma.category.findFirst({ where })
    return !!category
  }

  // Get category statistics
  static async getStatistics() {
    const [totalCategories, categoriesWithProducts, mostPopularCategory] = await Promise.all([
      prisma.category.count(),
      prisma.category.count({
        where: {
          products: {
            some: { isActive: true }
          }
        }
      }),
      prisma.category.findFirst({
        include: {
          _count: {
            select: { products: { where: { isActive: true } } }
          }
        },
        orderBy: {
          products: {
            _count: 'desc'
          }
        }
      })
    ])

    return {
      totalCategories,
      categoriesWithProducts,
      mostPopularCategory: mostPopularCategory ? {
        name: mostPopularCategory.name,
        productCount: mostPopularCategory._count.products
      } : null
    }
  }
}
