// Currency formatting utility
export function formatCurrency(amount: number, currency: string = 'IDR'): string {
  if (currency === 'IDR') {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
}

// Number formatting utility
export function formatNumber(value: number, decimals: number = 0): string {
  return new Intl.NumberFormat('id-ID', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

// Date formatting utility
export function formatDate(date: Date | string, format: 'short' | 'long' | 'datetime' = 'short'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  switch (format) {
    case 'long':
      return new Intl.DateTimeFormat('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(dateObj);
    
    case 'datetime':
      return new Intl.DateTimeFormat('id-ID', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(dateObj);
    
    default:
      return new Intl.DateTimeFormat('id-ID', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      }).format(dateObj);
  }
}

// Time formatting utility
export function formatTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(dateObj);
}

// Percentage formatting utility
export function formatPercentage(value: number, decimals: number = 1): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
}

// Transaction number formatting
export function generateTransactionNumber(): string {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-6);
  
  return `TXN-${year}${month}${day}-${timestamp}`;
}

// SKU formatting
export function generateSKU(category: string, index: number): string {
  const prefixes: { [key: string]: string } = {
    'Candy': 'CANDY',
    'Makanan': 'FOOD',
    'Minuman': 'DRINK',
    'Personal Care': 'CARE'
  };
  
  const prefix = prefixes[category] || 'PROD';
  return `${prefix}-${String(index).padStart(3, '0')}`;
}

// File size formatting
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Text truncation utility
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// Phone number formatting (Indonesian format)
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check if it starts with country code
  if (cleaned.startsWith('62')) {
    // Format: +62 xxx-xxxx-xxxx
    const match = cleaned.match(/^62(\d{3})(\d{4})(\d{4})$/);
    if (match) {
      return `+62 ${match[1]}-${match[2]}-${match[3]}`;
    }
  } else if (cleaned.startsWith('0')) {
    // Format: 0xxx-xxxx-xxxx
    const match = cleaned.match(/^0(\d{3})(\d{4})(\d{4})$/);
    if (match) {
      return `0${match[1]}-${match[2]}-${match[3]}`;
    }
  }
  
  return phone; // Return original if no pattern matches
}

// Capitalize first letter of each word
export function capitalizeWords(text: string): string {
  return text.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

// Convert to title case
export function toTitleCase(text: string): string {
  return text.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

// Validate and format Indonesian postal code
export function formatPostalCode(code: string): string {
  const cleaned = code.replace(/\D/g, '');
  if (cleaned.length === 5) {
    return cleaned;
  }
  return code; // Return original if not valid
}

// Format Indonesian ID number (NIK)
export function formatNIK(nik: string): string {
  const cleaned = nik.replace(/\D/g, '');
  if (cleaned.length === 16) {
    return cleaned.replace(/(\d{6})(\d{6})(\d{4})/, '$1-$2-$3');
  }
  return nik; // Return original if not valid
}
