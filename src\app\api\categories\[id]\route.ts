import { NextRequest, NextResponse } from 'next/server'
import { CategoryService } from '@/lib/models/category'
import { updateCategorySchema } from '@/lib/validations'
import { z } from 'zod'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = parseInt(params.id)
    
    if (isNaN(categoryId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid category ID'
      }, { status: 400 })
    }
    
    const category = await CategoryService.getById(categoryId)
    
    if (!category) {
      return NextResponse.json({
        success: false,
        error: 'Category not found'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      data: category
    })
  } catch (error) {
    console.error('Error fetching category:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch category'
    }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = parseInt(params.id)
    
    if (isNaN(categoryId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid category ID'
      }, { status: 400 })
    }
    
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateCategorySchema.parse(body)
    
    // Check if name already exists (excluding current category)
    if (validatedData.name) {
      const nameExists = await CategoryService.nameExists(validatedData.name, categoryId)
      if (nameExists) {
        return NextResponse.json({
          success: false,
          error: 'Category name already exists'
        }, { status: 409 })
      }
    }
    
    const category = await CategoryService.update(categoryId, validatedData)
    
    return NextResponse.json({
      success: true,
      data: category,
      message: 'Category updated successfully'
    })
  } catch (error) {
    console.error('Error updating category:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to update category'
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = parseInt(params.id)
    
    if (isNaN(categoryId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid category ID'
      }, { status: 400 })
    }
    
    await CategoryService.delete(categoryId)
    
    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting category:', error)
    
    if (error instanceof Error && error.message.includes('Cannot delete category that has products')) {
      return NextResponse.json({
        success: false,
        error: 'Cannot delete category that has products. Please remove or reassign products first.'
      }, { status: 409 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to delete category'
    }, { status: 500 })
  }
}
