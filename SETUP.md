# POS Arta Setup Guide

This guide will walk you through setting up the POS Arta application from scratch.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** (version 9 or higher)
- **PostgreSQL** (version 12 or higher)
- **Git**

## Step-by-Step Setup

### 1. Install Dependencies

```bash
npm install
```

This will install all the required dependencies including:
- Next.js 15 with React 19
- TypeScript
- PostgreSQL client (pg)
- Tailwind CSS
- Heroicons
- And other development dependencies

### 2. PostgreSQL Database Setup

#### Option A: Local PostgreSQL Installation

1. **Install PostgreSQL** on your system if not already installed
2. **Start PostgreSQL service**
3. **Create database and user**:

```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE pos_arta;

-- Create user
CREATE USER pos_user WITH PASSWORD 'your_secure_password';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE pos_arta TO pos_user;

-- Exit psql
\q
```

#### Option B: Docker PostgreSQL (Alternative)

```bash
# Run PostgreSQL in Docker
docker run --name pos-postgres \
  -e POSTGRES_DB=pos_arta \
  -e POSTGRES_USER=pos_user \
  -e POSTGRES_PASSWORD=your_secure_password \
  -p 5432:5432 \
  -d postgres:15
```

### 3. Environment Configuration

1. **Copy environment template**:
```bash
cp .env.example .env.local
```

2. **Edit `.env.local`** with your database credentials:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pos_arta
DB_USER=pos_user
DB_PASSWORD=your_secure_password

# JWT Configuration (generate a secure secret)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Application Configuration
NODE_ENV=development
PORT=3000

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key

# API Configuration
API_BASE_URL=http://localhost:3000/api

# Application Settings
COMPANY_NAME=Arta Store
CURRENCY=IDR
TAX_RATE=0.10
DEFAULT_LANGUAGE=id
```

### 4. Database Schema Setup

Initialize the database schema:

```bash
# Create all tables, indexes, and triggers
npm run db:setup
```

This command will:
- Create all database tables
- Set up relationships and constraints
- Create indexes for performance
- Set up triggers for automatic updates

### 5. Data Migration

Migrate the product data from JSON to PostgreSQL:

```bash
# Run the migration script
npm run migrate
```

This will:
- Extract categories from product data
- Normalize packaging types
- Insert all products with proper relationships
- Initialize inventory with default stock levels
- Create default user accounts

### 6. Verify Setup

1. **Check database connection**:
```bash
# Test database connectivity
psql -h localhost -U pos_user -d pos_arta -c "SELECT COUNT(*) FROM products;"
```

2. **Verify data migration**:
```sql
-- Connect to database
psql -h localhost -U pos_user -d pos_arta

-- Check tables and data
\dt                           -- List all tables
SELECT COUNT(*) FROM categories;     -- Should return 4
SELECT COUNT(*) FROM products;       -- Should return 77
SELECT COUNT(*) FROM inventory;      -- Should return 77
\q
```

### 7. Start Development Server

```bash
npm run dev
```

The application will be available at:
- **Main app**: http://localhost:3000
- **POS Interface**: http://localhost:3000/pos

## Verification Checklist

- [ ] Node.js and npm are installed
- [ ] PostgreSQL is running
- [ ] Database `pos_arta` exists
- [ ] User `pos_user` has access to database
- [ ] Environment variables are configured
- [ ] Database schema is created (8 tables)
- [ ] Product data is migrated (77 products, 4 categories)
- [ ] Development server starts without errors
- [ ] POS interface loads at `/pos`

## Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```
**Solution**: Ensure PostgreSQL is running and credentials are correct.

#### 2. Permission Denied
```
Error: permission denied for database pos_arta
```
**Solution**: Grant proper privileges to the user:
```sql
GRANT ALL PRIVILEGES ON DATABASE pos_arta TO pos_user;
GRANT ALL ON SCHEMA public TO pos_user;
```

#### 3. Migration Script Fails
```
Error: relation "categories" does not exist
```
**Solution**: Run the schema setup first:
```bash
npm run db:setup
```

#### 4. Port Already in Use
```
Error: listen EADDRINUSE: address already in use :::3000
```
**Solution**: Kill the process using port 3000 or change the port:
```bash
# Kill process on port 3000
npx kill-port 3000

# Or change port in .env.local
PORT=3001
```

### Database Reset

If you need to reset the database:

```sql
-- Connect as superuser
psql -U postgres

-- Drop and recreate database
DROP DATABASE IF EXISTS pos_arta;
CREATE DATABASE pos_arta;
GRANT ALL PRIVILEGES ON DATABASE pos_arta TO pos_user;
```

Then re-run setup:
```bash
npm run db:setup
npm run migrate
```

## Next Steps

After successful setup:

1. **Explore the POS Interface**: Navigate to `/pos` to see the cashier interface
2. **Test Product Search**: Use the search functionality to find products
3. **Browse Categories**: Filter products by category
4. **Add to Cart**: Click on products to add them to the shopping cart
5. **API Testing**: Use tools like Postman to test API endpoints

## Development Workflow

1. **Make changes** to code
2. **Test locally** with `npm run dev`
3. **Run tests** with `npm test` (when implemented)
4. **Check linting** with `npm run lint`
5. **Build for production** with `npm run build`

## Production Deployment

For production deployment, you'll need to:

1. Set up a production PostgreSQL database
2. Configure production environment variables
3. Build the application: `npm run build`
4. Start the production server: `npm start`
5. Set up SSL certificates
6. Configure reverse proxy (nginx/Apache)
7. Set up monitoring and logging

## Support

If you encounter issues:

1. Check this setup guide
2. Review the main README.md
3. Check the troubleshooting section
4. Create an issue in the repository
5. Contact the development team
