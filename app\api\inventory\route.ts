import { NextRequest, NextResponse } from 'next/server'
import { InventoryService } from '@/lib/models/inventory'
import { paginationSchema } from '@/lib/validations'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      search: searchParams.get('search') || '',
      sortBy: searchParams.get('sort_by') || 'productName',
      sortOrder: (searchParams.get('sort_order') || 'asc') as 'asc' | 'desc',
      lowStock: searchParams.get('low_stock') === 'true',
      outOfStock: searchParams.get('out_of_stock') === 'true'
    }

    // Validate pagination parameters
    const validatedParams = paginationSchema.parse(params)
    
    const result = await InventoryService.getAll(validatedParams)
    
    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching inventory:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid parameters',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch inventory'
    }, { status: 500 })
  }
}

// Get inventory alerts (low stock, out of stock)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type } = body
    
    if (!type || !['low_stock', 'out_of_stock', 'all'].includes(type)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid alert type. Must be: low_stock, out_of_stock, or all'
      }, { status: 400 })
    }
    
    const alerts = await InventoryService.getAlerts(type)
    
    return NextResponse.json({
      success: true,
      data: alerts
    })
  } catch (error) {
    console.error('Error fetching inventory alerts:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch inventory alerts'
    }, { status: 500 })
  }
}
