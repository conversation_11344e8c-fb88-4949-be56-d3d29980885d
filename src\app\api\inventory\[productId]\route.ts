import { NextRequest, NextResponse } from 'next/server'
import { InventoryService } from '@/lib/models/inventory'
import { updateInventorySchema } from '@/lib/validations'
import { z } from 'zod'

export async function GET(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const productId = parseInt(params.productId)
    
    if (isNaN(productId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid product ID'
      }, { status: 400 })
    }
    
    const inventory = await InventoryService.getByProductId(productId)
    
    if (!inventory) {
      return NextResponse.json({
        success: false,
        error: 'Inventory record not found'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      data: inventory
    })
  } catch (error) {
    console.error('Error fetching inventory:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch inventory'
    }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const productId = parseInt(params.productId)
    
    if (isNaN(productId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid product ID'
      }, { status: 400 })
    }
    
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateInventorySchema.parse(body)
    
    // Check if inventory record exists
    const existingInventory = await InventoryService.getByProductId(productId)
    if (!existingInventory) {
      return NextResponse.json({
        success: false,
        error: 'Inventory record not found'
      }, { status: 404 })
    }
    
    const updatedInventory = await InventoryService.update(productId, validatedData)
    
    return NextResponse.json({
      success: true,
      data: updatedInventory,
      message: 'Inventory updated successfully'
    })
  } catch (error) {
    console.error('Error updating inventory:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to update inventory'
    }, { status: 500 })
  }
}

// Adjust stock (add or remove stock with reason)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const productId = parseInt(params.productId)
    
    if (isNaN(productId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid product ID'
      }, { status: 400 })
    }
    
    const body = await request.json()
    const { adjustment, reason, referenceType, referenceId } = body
    
    if (typeof adjustment !== 'number' || adjustment === 0) {
      return NextResponse.json({
        success: false,
        error: 'Adjustment must be a non-zero number'
      }, { status: 400 })
    }
    
    if (!reason || typeof reason !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Reason is required'
      }, { status: 400 })
    }
    
    // Check if inventory record exists
    const existingInventory = await InventoryService.getByProductId(productId)
    if (!existingInventory) {
      return NextResponse.json({
        success: false,
        error: 'Inventory record not found'
      }, { status: 404 })
    }
    
    const result = await InventoryService.adjustStock(
      productId,
      adjustment,
      reason,
      referenceType || 'MANUAL',
      referenceId
    )
    
    return NextResponse.json({
      success: true,
      data: result,
      message: `Stock ${adjustment > 0 ? 'increased' : 'decreased'} successfully`
    })
  } catch (error) {
    console.error('Error adjusting stock:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to adjust stock'
    }, { status: 500 })
  }
}
