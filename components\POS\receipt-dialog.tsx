'use client'

import { useRef } from 'react'
import { Printer, X } from 'lucide-react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { formatCurrency, formatDate } from '@/utils/format'
import type { SalesTransactionWithRelations } from '@/types'

interface ReceiptDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transaction: SalesTransactionWithRelations
}

export function ReceiptDialog({ open, onOpenChange, transaction }: ReceiptDialogProps) {
  const receiptRef = useRef<HTMLDivElement>(null)

  const handlePrint = () => {
    if (receiptRef.current) {
      const printContent = receiptRef.current.innerHTML
      const originalContent = document.body.innerHTML
      
      document.body.innerHTML = printContent
      window.print()
      document.body.innerHTML = originalContent
      window.location.reload() // Reload to restore React functionality
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>Receipt</DialogTitle>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="icon"
                onClick={handlePrint}
                title="Print Receipt"
              >
                <Printer className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Receipt Content */}
        <div ref={receiptRef} className="font-mono text-sm">
          <Card>
            <CardContent className="p-6">
              {/* Store Header */}
              <div className="text-center mb-6">
                <h1 className="text-xl font-bold">ARTA STORE</h1>
                <p className="text-muted-foreground">Point of Sale System</p>
                <p className="text-muted-foreground">Jl. Contoh No. 123, Jakarta</p>
                <p className="text-muted-foreground">Tel: (021) 1234-5678</p>
              </div>

              {/* Transaction Info */}
              <div className="border-t border-b py-3 mb-4 space-y-1">
                <div className="flex justify-between">
                  <span>Transaction #:</span>
                  <span className="font-semibold">{transaction.transactionNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span>Date:</span>
                  <span>{formatDate(transaction.createdAt, 'datetime')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cashier:</span>
                  <span>{transaction.user?.fullName || 'Unknown'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Payment:</span>
                  <span className="uppercase">{transaction.paymentMethod}</span>
                </div>
              </div>

              {/* Items */}
              <div className="mb-4">
                <div className="border-b pb-2 mb-2">
                  <div className="flex justify-between font-semibold">
                    <span>ITEM</span>
                    <span>TOTAL</span>
                  </div>
                </div>
                
                {transaction.items.map((item, index) => (
                  <div key={index} className="mb-3">
                    <div className="flex justify-between">
                      <span className="flex-1 truncate pr-2">
                        {item.product.name}
                      </span>
                      <span className="font-semibold">
                        {formatCurrency(item.totalPrice.toNumber())}
                      </span>
                    </div>
                    <div className="text-muted-foreground text-xs flex justify-between">
                      <span>
                        {item.quantity} x {formatCurrency(item.unitPrice.toNumber())}
                      </span>
                      {item.product.sku && (
                        <span>SKU: {item.product.sku}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Totals */}
              <div className="border-t pt-3 space-y-1">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(transaction.totalAmount.toNumber())}</span>
                </div>
                
                {transaction.discountAmount.toNumber() > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount:</span>
                    <span>-{formatCurrency(transaction.discountAmount.toNumber())}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span>Tax (10%):</span>
                  <span>{formatCurrency(transaction.taxAmount.toNumber())}</span>
                </div>
                
                <div className="border-t pt-1">
                  <div className="flex justify-between font-bold text-lg">
                    <span>TOTAL:</span>
                    <span>{formatCurrency(transaction.finalAmount.toNumber())}</span>
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <span>Payment Received:</span>
                  <span>{formatCurrency(transaction.paymentReceived.toNumber())}</span>
                </div>
                
                <div className="flex justify-between font-semibold">
                  <span>Change:</span>
                  <span>{formatCurrency(transaction.changeAmount.toNumber())}</span>
                </div>
              </div>

              {/* Notes */}
              {transaction.notes && (
                <div className="border-t pt-3 mt-3">
                  <div className="text-muted-foreground">
                    <span className="font-semibold">Notes:</span>
                    <p className="mt-1">{transaction.notes}</p>
                  </div>
                </div>
              )}

              {/* Footer */}
              <div className="border-t pt-4 mt-6 text-center text-xs text-muted-foreground">
                <p>Thank you for shopping with us!</p>
                <p>Please keep this receipt for your records</p>
                <p className="mt-2">
                  Printed on {formatDate(new Date(), 'datetime')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-4">
          <Button
            onClick={handlePrint}
            className="flex-1"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print Receipt
          </Button>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
