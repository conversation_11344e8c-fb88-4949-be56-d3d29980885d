import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { Cart } from '@/components/pos/cart'
import type { CartItem } from '@/types'

const mockCartItems: CartItem[] = [
  {
    product: {
      id: 1,
      name: 'Chocolate Bar',
      categoryId: 1,
      packagingDescription: '50g bar',
      pricePerPackage: 2.50,
      unitsPerPackage: 1,
      packagingTypeId: 1,
      unitPrice: 2.50,
      sku: 'CHOC001',
      barcode: '1234567890123',
      description: 'Milk chocolate bar',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      category: { id: 1, name: 'Candy', description: 'Sweet treats', createdAt: new Date(), updatedAt: new Date() },
      packagingType: { id: 1, name: 'Bar', description: 'Bar packaging', createdAt: new Date() },
      currentStock: 100,
      categoryName: 'Candy',
      packagingTypeName: 'Bar'
    },
    quantity: 2,
    unitPrice: 2.50,
    totalPrice: 5.00
  },
  {
    product: {
      id: 2,
      name: 'Energy Drink',
      categoryId: 2,
      packagingDescription: '250ml can',
      pricePerPackage: 3.99,
      unitsPerPackage: 1,
      packagingTypeId: 2,
      unitPrice: 3.99,
      sku: 'ENERGY001',
      barcode: '1234567890124',
      description: 'Energy drink',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      category: { id: 2, name: 'Drinks', description: 'Beverages', createdAt: new Date(), updatedAt: new Date() },
      packagingType: { id: 2, name: 'Can', description: 'Can packaging', createdAt: new Date() },
      currentStock: 50,
      categoryName: 'Drinks',
      packagingTypeName: 'Can'
    },
    quantity: 1,
    unitPrice: 3.99,
    totalPrice: 3.99
  }
]

describe('Cart', () => {
  const mockOnUpdateQuantity = jest.fn()
  const mockOnRemoveItem = jest.fn()
  const mockOnCheckout = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders cart with items', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    expect(screen.getByText('Cart (2)')).toBeInTheDocument()
    expect(screen.getByText('Chocolate Bar')).toBeInTheDocument()
    expect(screen.getByText('Energy Drink')).toBeInTheDocument()
  })

  it('displays correct quantities and prices', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    // Check quantities
    const quantityInputs = screen.getAllByRole('spinbutton')
    expect(quantityInputs[0]).toHaveValue(2)
    expect(quantityInputs[1]).toHaveValue(1)

    // Check individual prices
    expect(screen.getByText('$2.50')).toBeInTheDocument() // Unit price
    expect(screen.getByText('$3.99')).toBeInTheDocument() // Unit price

    // Check total prices
    expect(screen.getByText('$5.00')).toBeInTheDocument() // 2 x $2.50
    expect(screen.getByText('$3.99')).toBeInTheDocument() // 1 x $3.99
  })

  it('calculates totals correctly', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    // Subtotal: $5.00 + $3.99 = $8.99
    expect(screen.getByText('$8.99')).toBeInTheDocument()

    // Tax (10%): $0.90
    expect(screen.getByText('$0.90')).toBeInTheDocument()

    // Total: $8.99 + $0.90 = $9.89
    expect(screen.getByText('$9.89')).toBeInTheDocument()
  })

  it('calls onUpdateQuantity when quantity is changed', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const quantityInputs = screen.getAllByRole('spinbutton')
    fireEvent.change(quantityInputs[0], { target: { value: '3' } })

    expect(mockOnUpdateQuantity).toHaveBeenCalledWith(1, 3) // productId, newQuantity
  })

  it('calls onUpdateQuantity when plus button is clicked', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const plusButtons = screen.getAllByRole('button', { name: /increase quantity/i })
    fireEvent.click(plusButtons[0])

    expect(mockOnUpdateQuantity).toHaveBeenCalledWith(1, 3) // Increase from 2 to 3
  })

  it('calls onUpdateQuantity when minus button is clicked', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const minusButtons = screen.getAllByRole('button', { name: /decrease quantity/i })
    fireEvent.click(minusButtons[0])

    expect(mockOnUpdateQuantity).toHaveBeenCalledWith(1, 1) // Decrease from 2 to 1
  })

  it('calls onRemoveItem when remove button is clicked', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const removeButtons = screen.getAllByRole('button', { name: /remove/i })
    fireEvent.click(removeButtons[0])

    expect(mockOnRemoveItem).toHaveBeenCalledWith(1) // productId
  })

  it('calls onCheckout when checkout button is clicked', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const checkoutButton = screen.getByRole('button', { name: /checkout/i })
    fireEvent.click(checkoutButton)

    expect(mockOnCheckout).toHaveBeenCalled()
  })

  it('disables checkout button when cart is empty', () => {
    render(
      <Cart
        items={[]}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const checkoutButton = screen.getByRole('button', { name: /checkout/i })
    expect(checkoutButton).toBeDisabled()
  })

  it('shows empty cart message when no items', () => {
    render(
      <Cart
        items={[]}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    expect(screen.getByText('Cart (0)')).toBeInTheDocument()
    expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument()
  })

  it('prevents quantity from going below 1', () => {
    const singleItemCart = [
      {
        ...mockCartItems[0],
        quantity: 1
      }
    ]

    render(
      <Cart
        items={singleItemCart}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const minusButton = screen.getByRole('button', { name: /decrease quantity/i })
    expect(minusButton).toBeDisabled()
  })

  it('displays product SKU when available', () => {
    render(
      <Cart
        items={mockCartItems}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    expect(screen.getByText('CHOC001')).toBeInTheDocument()
    expect(screen.getByText('ENERGY001')).toBeInTheDocument()
  })

  it('handles large quantities correctly', () => {
    const largeQuantityCart = [
      {
        ...mockCartItems[0],
        quantity: 999,
        totalPrice: 999 * 2.50
      }
    ]

    render(
      <Cart
        items={largeQuantityCart}
        onUpdateQuantity={mockOnUpdateQuantity}
        onRemoveItem={mockOnRemoveItem}
        onCheckout={mockOnCheckout}
      />
    )

    const quantityInput = screen.getByRole('spinbutton')
    expect(quantityInput).toHaveValue(999)

    // Check that total is calculated correctly
    expect(screen.getByText('$2,497.50')).toBeInTheDocument() // 999 * $2.50
  })
})
