import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/sales/route'
import { GET as GetById, PATCH } from '@/app/api/sales/[id]/route'
import { prisma } from '@/lib/prisma'

// Mock the Prisma client
jest.mock('@/lib/prisma')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/sales', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/sales', () => {
    it('should return sales transactions with pagination', async () => {
      const mockTransactions = [
        {
          id: 1,
          transactionNumber: 'TXN-001',
          userId: 1,
          totalAmount: 10.00,
          taxAmount: 1.00,
          discountAmount: 0.00,
          finalAmount: 11.00,
          paymentMethod: 'CASH',
          paymentReceived: 15.00,
          changeAmount: 4.00,
          status: 'COMPLETED',
          notes: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          user: { fullName: '<PERSON>' },
          items: [
            {
              id: 1,
              productId: 1,
              quantity: 2,
              unitPrice: 5.00,
              totalPrice: 10.00,
              product: { name: 'Chocolate Bar', sku: 'CHOC001' }
            }
          ]
        }
      ]

      mockPrisma.salesTransaction.findMany.mockResolvedValue(mockTransactions as any)
      mockPrisma.salesTransaction.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/sales')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].transactionNumber).toBe('TXN-001')
      expect(data.data.pagination.totalItems).toBe(1)
    })

    it('should filter transactions by date range', async () => {
      mockPrisma.salesTransaction.findMany.mockResolvedValue([])
      mockPrisma.salesTransaction.count.mockResolvedValue(0)

      const request = new NextRequest('http://localhost:3000/api/sales?start_date=2024-01-01&end_date=2024-01-31')
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockPrisma.salesTransaction.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            createdAt: expect.objectContaining({
              gte: expect.any(Date),
              lte: expect.any(Date)
            })
          })
        })
      )
    })

    it('should filter transactions by payment method', async () => {
      mockPrisma.salesTransaction.findMany.mockResolvedValue([])
      mockPrisma.salesTransaction.count.mockResolvedValue(0)

      const request = new NextRequest('http://localhost:3000/api/sales?payment_method=CASH')
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockPrisma.salesTransaction.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            paymentMethod: 'CASH'
          })
        })
      )
    })
  })

  describe('POST /api/sales', () => {
    it('should create a new sales transaction', async () => {
      const newTransaction = {
        items: [
          {
            productId: 1,
            quantity: 2,
            unitPrice: 5.00
          }
        ],
        paymentMethod: 'CASH',
        paymentReceived: 15.00,
        discountAmount: 0.00,
        notes: 'Test transaction'
      }

      const mockProduct = {
        id: 1,
        name: 'Chocolate Bar',
        pricePerPackage: 5.00,
        inventory: { currentStock: 100 }
      }

      const mockCreatedTransaction = {
        id: 1,
        transactionNumber: 'TXN-001',
        userId: 1,
        totalAmount: 10.00,
        taxAmount: 1.00,
        discountAmount: 0.00,
        finalAmount: 11.00,
        paymentMethod: 'CASH',
        paymentReceived: 15.00,
        changeAmount: 4.00,
        status: 'COMPLETED',
        notes: 'Test transaction',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockPrisma.product.findUnique.mockResolvedValue(mockProduct as any)
      mockPrisma.$transaction.mockResolvedValue(mockCreatedTransaction as any)

      const request = new NextRequest('http://localhost:3000/api/sales', {
        method: 'POST',
        body: JSON.stringify(newTransaction)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.transactionNumber).toBe('TXN-001')
      expect(data.message).toBe('Sales transaction created successfully')
    })

    it('should return error for insufficient stock', async () => {
      const newTransaction = {
        items: [
          {
            productId: 1,
            quantity: 200, // More than available stock
            unitPrice: 5.00
          }
        ],
        paymentMethod: 'CASH',
        paymentReceived: 1000.00
      }

      const mockProduct = {
        id: 1,
        name: 'Chocolate Bar',
        inventory: { currentStock: 10 } // Only 10 in stock
      }

      mockPrisma.product.findUnique.mockResolvedValue(mockProduct as any)

      const request = new NextRequest('http://localhost:3000/api/sales', {
        method: 'POST',
        body: JSON.stringify(newTransaction)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Insufficient stock')
    })

    it('should return error for non-existent product', async () => {
      const newTransaction = {
        items: [
          {
            productId: 999, // Non-existent product
            quantity: 1,
            unitPrice: 5.00
          }
        ],
        paymentMethod: 'CASH',
        paymentReceived: 10.00
      }

      mockPrisma.product.findUnique.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/sales', {
        method: 'POST',
        body: JSON.stringify(newTransaction)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Product not found')
    })

    it('should return validation error for invalid data', async () => {
      const invalidTransaction = {
        items: [], // Empty items array
        paymentMethod: 'INVALID_METHOD',
        paymentReceived: -10.00 // Negative amount
      }

      const request = new NextRequest('http://localhost:3000/api/sales', {
        method: 'POST',
        body: JSON.stringify(invalidTransaction)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation failed')
      expect(data.details).toBeDefined()
    })
  })

  describe('GET /api/sales/[id]', () => {
    it('should return transaction by ID', async () => {
      const mockTransaction = {
        id: 1,
        transactionNumber: 'TXN-001',
        totalAmount: 10.00,
        status: 'COMPLETED',
        user: { fullName: 'John Doe' },
        items: [
          {
            id: 1,
            productId: 1,
            quantity: 2,
            unitPrice: 5.00,
            totalPrice: 10.00,
            product: { name: 'Chocolate Bar', sku: 'CHOC001' }
          }
        ]
      }

      mockPrisma.salesTransaction.findUnique.mockResolvedValue(mockTransaction as any)

      const response = await GetById(
        new NextRequest('http://localhost:3000/api/sales/1'),
        { params: { id: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.id).toBe(1)
      expect(data.data.transactionNumber).toBe('TXN-001')
    })

    it('should return 404 for non-existent transaction', async () => {
      mockPrisma.salesTransaction.findUnique.mockResolvedValue(null)

      const response = await GetById(
        new NextRequest('http://localhost:3000/api/sales/999'),
        { params: { id: '999' } }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Transaction not found')
    })
  })

  describe('PATCH /api/sales/[id]', () => {
    it('should update transaction status', async () => {
      const updateData = {
        status: 'REFUNDED',
        notes: 'Customer requested refund'
      }

      const existingTransaction = {
        id: 1,
        status: 'COMPLETED',
        transactionNumber: 'TXN-001'
      }

      const updatedTransaction = {
        ...existingTransaction,
        ...updateData,
        updatedAt: new Date()
      }

      mockPrisma.salesTransaction.findUnique.mockResolvedValue(existingTransaction as any)
      mockPrisma.salesTransaction.update.mockResolvedValue(updatedTransaction as any)

      const response = await PATCH(
        new NextRequest('http://localhost:3000/api/sales/1', {
          method: 'PATCH',
          body: JSON.stringify(updateData)
        }),
        { params: { id: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.status).toBe('REFUNDED')
      expect(data.message).toBe('Transaction status updated successfully')
    })

    it('should prevent invalid status changes', async () => {
      const updateData = {
        status: 'PENDING'
      }

      const existingTransaction = {
        id: 1,
        status: 'CANCELLED'
      }

      mockPrisma.salesTransaction.findUnique.mockResolvedValue(existingTransaction as any)

      const response = await PATCH(
        new NextRequest('http://localhost:3000/api/sales/1', {
          method: 'PATCH',
          body: JSON.stringify(updateData)
        }),
        { params: { id: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Cannot change status of cancelled transaction')
    })
  })
})
