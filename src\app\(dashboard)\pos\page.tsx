"use client";

import { Cart } from "@/components/pos/cart";
import { CategorySidebar } from "@/components/pos/category-sidebar";
import { CheckoutDialog } from "@/components/pos/checkout-dialog";
import { ProductGrid } from "@/components/pos/product-grid";
import { ReceiptDialog } from "@/components/pos/receipt-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import type {
  CartItem,
  Category,
  CreateSalesTransactionInput,
  Product,
} from "@/types";
import { Menu, Search } from "lucide-react";
import { useEffect, useState } from "react";

export default function POSPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showCheckout, setShowCheckout] = useState(false);
  const [showReceipt, setShowReceipt] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState<any>(null);
  const { toast } = useToast();

  // Tax rate (10%)
  const TAX_RATE = 0.1;

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/categories");
      const data = await response.json();
      if (data.success) {
        setCategories(data.data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast({
        title: "Error",
        description: "Failed to fetch categories",
        variant: "destructive",
      });
    }
  };

  const addToCart = (product: Product, quantity: number = 1) => {
    setCartItems((prevItems) => {
      const existingItem = prevItems.find(
        (item) => item.product.id === product.id
      );

      if (existingItem) {
        return prevItems.map((item) =>
          item.product.id === product.id
            ? {
                ...item,
                quantity: item.quantity + quantity,
                totalPrice: (item.quantity + quantity) * item.unitPrice,
              }
            : item
        );
      } else {
        return [
          ...prevItems,
          {
            product,
            quantity,
            unitPrice:
              product.unitPrice ||
              product.pricePerPackage / product.unitsPerPackage,
            totalPrice:
              quantity *
              (product.unitPrice ||
                product.pricePerPackage / product.unitsPerPackage),
          },
        ];
      }
    });

    toast({
      title: "Added to cart",
      description: `${product.name} added to cart`,
    });
  };

  const updateQuantity = (productId: number, quantity: number) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.product.id === productId
          ? {
              ...item,
              quantity,
              totalPrice: quantity * item.unitPrice,
            }
          : item
      )
    );
  };

  const removeItem = (productId: number) => {
    setCartItems((prevItems) =>
      prevItems.filter((item) => item.product.id !== productId)
    );
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const handleCheckout = () => {
    if (cartItems.length === 0) {
      toast({
        title: "Cart is empty",
        description: "Add some products to cart before checkout",
        variant: "destructive",
      });
      return;
    }
    setShowCheckout(true);
  };

  const handleConfirmSale = async (
    transactionData: CreateSalesTransactionInput
  ) => {
    try {
      const response = await fetch("/api/sales", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(transactionData),
      });

      const result = await response.json();

      if (result.success) {
        // Clear cart
        setCartItems([]);
        setShowCheckout(false);

        // Set transaction for receipt
        setCurrentTransaction(result.data);
        setShowReceipt(true);

        toast({
          title: "Sale completed",
          description: `Transaction ${result.data.transactionNumber} completed successfully`,
        });
      } else {
        throw new Error(result.error || "Failed to process transaction");
      }
    } catch (error) {
      console.error("Transaction error:", error);
      toast({
        title: "Transaction failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to process transaction",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Calculate totals
  const subtotal = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
  const discountAmount = 0; // TODO: Implement discount logic
  const taxAmount = (subtotal - discountAmount) * TAX_RATE;
  const total = subtotal - discountAmount + taxAmount;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowSidebar(!showSidebar)}
              >
                <Menu className="h-6 w-6" />
              </Button>
              <h1 className="text-xl font-bold text-gray-800">POS Arta</h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>

              <div className="text-sm text-gray-600">
                <span className="font-medium">Cashier:</span> Admin
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-73px)]">
        {/* Sidebar - Categories */}
        <CategorySidebar
          isOpen={showSidebar}
          categories={categories}
          selectedCategory={selectedCategory}
          onSelectCategory={setSelectedCategory}
        />

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Product Grid */}
          <div className="flex-1 p-6 overflow-y-auto">
            <ProductGrid
              onAddToCart={addToCart}
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
            />
          </div>

          {/* Cart */}
          <div className="w-96 p-6">
            <Cart
              items={cartItems}
              onUpdateQuantity={updateQuantity}
              onRemoveItem={removeItem}
              onClearCart={clearCart}
              onCheckout={handleCheckout}
              subtotal={subtotal}
              taxAmount={taxAmount}
              discountAmount={discountAmount}
              total={total}
            />
          </div>
        </div>
      </div>

      {/* Checkout Dialog */}
      <CheckoutDialog
        open={showCheckout}
        onOpenChange={setShowCheckout}
        cartItems={cartItems}
        subtotal={subtotal}
        taxAmount={taxAmount}
        discountAmount={discountAmount}
        total={total}
        onConfirmSale={handleConfirmSale}
      />

      {/* Receipt Dialog */}
      {currentTransaction && (
        <ReceiptDialog
          open={showReceipt}
          onOpenChange={setShowReceipt}
          transaction={currentTransaction}
        />
      )}
    </div>
  );
}
