import { ProductGrid } from "@/components/pos/product-grid";
import type { ProductWithRelations } from "@/types";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";

// Mock the toast hook
jest.mock("@/hooks/use-toast", () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Mock fetch
global.fetch = jest.fn();

const mockProducts: ProductWithRelations[] = [
  {
    id: 1,
    name: "Chocolate Bar",
    categoryId: 1,
    packagingDescription: "50g bar",
    pricePerPackage: 2.5,
    unitsPerPackage: 1,
    packagingTypeId: 1,
    unitPrice: 2.5,
    sku: "CHOC001",
    barcode: "1234567890123",
    description: "Milk chocolate bar",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    category: {
      id: 1,
      name: "Candy",
      description: "Sweet treats",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    packagingType: {
      id: 1,
      name: "<PERSON>",
      description: "Bar packaging",
      createdAt: new Date(),
    },
    currentStock: 100,
    categoryName: "Candy",
    packagingTypeName: "Bar",
  },
  {
    id: 2,
    name: "Energy Drink",
    categoryId: 2,
    packagingDescription: "250ml can",
    pricePerPackage: 3.99,
    unitsPerPackage: 1,
    packagingTypeId: 2,
    unitPrice: 3.99,
    sku: "ENERGY001",
    barcode: "1234567890124",
    description: "Energy drink",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    category: {
      id: 2,
      name: "Drinks",
      description: "Beverages",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    packagingType: {
      id: 2,
      name: "Can",
      description: "Can packaging",
      createdAt: new Date(),
    },
    currentStock: 5, // Low stock
    categoryName: "Drinks",
    packagingTypeName: "Can",
  },
  {
    id: 3,
    name: "Soap",
    categoryId: 3,
    packagingDescription: "100g bar",
    pricePerPackage: 1.25,
    unitsPerPackage: 1,
    packagingTypeId: 1,
    unitPrice: 1.25,
    sku: "SOAP001",
    barcode: "1234567890125",
    description: "Hand soap",
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    category: {
      id: 3,
      name: "Personal Care",
      description: "Personal care items",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    packagingType: {
      id: 1,
      name: "Bar",
      description: "Bar packaging",
      createdAt: new Date(),
    },
    currentStock: 0, // Out of stock
    categoryName: "Personal Care",
    packagingTypeName: "Bar",
  },
];

describe("ProductGrid", () => {
  const mockOnAddToCart = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          data: mockProducts,
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 3,
            itemsPerPage: 20,
            hasNext: false,
            hasPrev: false,
          },
        },
      }),
    });
  });

  it("renders product grid with products", async () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      expect(screen.getByText("Chocolate Bar")).toBeInTheDocument();
      expect(screen.getByText("Energy Drink")).toBeInTheDocument();
      expect(screen.getByText("Soap")).toBeInTheDocument();
    });
  });

  it("displays product information correctly", async () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      // Check product names
      expect(screen.getByText("Chocolate Bar")).toBeInTheDocument();

      // Check prices
      expect(screen.getByText("$2.50")).toBeInTheDocument();
      expect(screen.getByText("$3.99")).toBeInTheDocument();
      expect(screen.getByText("$1.25")).toBeInTheDocument();

      // Check categories
      expect(screen.getByText("Candy")).toBeInTheDocument();
      expect(screen.getByText("Drinks")).toBeInTheDocument();
      expect(screen.getByText("Personal Care")).toBeInTheDocument();
    });
  });

  it("displays stock status badges correctly", async () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      // Should show "In Stock" for chocolate bar (100 stock)
      expect(screen.getByText("In Stock")).toBeInTheDocument();

      // Should show "Low Stock" for energy drink (5 stock)
      expect(screen.getByText("Low Stock")).toBeInTheDocument();

      // Should show "Out of Stock" for soap (0 stock)
      expect(screen.getByText("Out of Stock")).toBeInTheDocument();
    });
  });

  it("calls onAddToCart when add button is clicked for in-stock item", async () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      const addButtons = screen.getAllByText("Add to Cart");
      // Click the first add button (Chocolate Bar)
      fireEvent.click(addButtons[0]);
    });

    expect(mockOnAddToCart).toHaveBeenCalledWith(mockProducts[0]);
  });

  it("disables add button for out of stock items", async () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      const addButtons = screen.getAllByRole("button", {
        name: /add to cart/i,
      });
      // The soap product (out of stock) button should be disabled
      const soapButton = addButtons.find((button) =>
        button.closest("[data-testid]")?.textContent?.includes("Soap")
      );
      expect(soapButton).toBeDisabled();
    });
  });

  it("filters products by category", async () => {
    // Mock fetch for category filter
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          data: [mockProducts[0]], // Only candy products
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 1,
            itemsPerPage: 20,
            hasNext: false,
            hasPrev: false,
          },
        },
      }),
    });

    render(
      <ProductGrid selectedCategoryId={1} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      expect(screen.getByText("Chocolate Bar")).toBeInTheDocument();
      expect(screen.queryByText("Energy Drink")).not.toBeInTheDocument();
      expect(screen.queryByText("Soap")).not.toBeInTheDocument();
    });

    // Verify the API was called with category filter
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining("category_id=1")
    );
  });

  it("shows loading state initially", () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    // Should show loading indicator
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it("shows error state when fetch fails", async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(
      new Error("Network error")
    );

    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      expect(screen.getByText(/failed to load products/i)).toBeInTheDocument();
    });
  });

  it("shows empty state when no products found", async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          data: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: 20,
            hasNext: false,
            hasPrev: false,
          },
        },
      }),
    });

    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      expect(screen.getByText(/no products found/i)).toBeInTheDocument();
    });
  });

  it("handles search functionality", async () => {
    const { rerender } = render(
      <ProductGrid
        selectedCategoryId={null}
        onAddToCart={mockOnAddToCart}
        searchTerm=""
      />
    );

    // Mock search results
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          data: [mockProducts[0]], // Only chocolate bar matches
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 1,
            itemsPerPage: 20,
            hasNext: false,
            hasPrev: false,
          },
        },
      }),
    });

    // Trigger search
    rerender(
      <ProductGrid
        selectedCategoryId={null}
        onAddToCart={mockOnAddToCart}
        searchTerm="chocolate"
      />
    );

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining("search=chocolate")
      );
    });
  });

  it("displays product SKU when available", async () => {
    render(
      <ProductGrid selectedCategoryId={null} onAddToCart={mockOnAddToCart} />
    );

    await waitFor(() => {
      expect(screen.getByText("CHOC001")).toBeInTheDocument();
      expect(screen.getByText("ENERGY001")).toBeInTheDocument();
      expect(screen.getByText("SOAP001")).toBeInTheDocument();
    });
  });
});
