import {
  createCategorySchema,
  updateCategorySchema,
  createProductSchema,
  updateProductSchema,
  createSalesTransactionSchema,
  updateInventorySchema,
  paginationSchema,
  productFiltersSchema,
  transactionFiltersSchema
} from '@/lib/validations'

describe('Validation Schemas', () => {
  describe('createCategorySchema', () => {
    it('validates valid category data', () => {
      const validData = {
        name: 'Electronics',
        description: 'Electronic devices and accessories'
      }

      const result = createCategorySchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe('Electronics')
        expect(result.data.description).toBe('Electronic devices and accessories')
      }
    })

    it('validates category with only name', () => {
      const validData = {
        name: 'Books'
      }

      const result = createCategorySchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe('Books')
        expect(result.data.description).toBeUndefined()
      }
    })

    it('rejects empty name', () => {
      const invalidData = {
        name: '',
        description: 'Some description'
      }

      const result = createCategorySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 1 character')
      }
    })

    it('rejects missing name', () => {
      const invalidData = {
        description: 'Some description'
      }

      const result = createCategorySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Required')
      }
    })

    it('rejects name longer than 100 characters', () => {
      const invalidData = {
        name: 'a'.repeat(101)
      }

      const result = createCategorySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at most 100 characters')
      }
    })
  })

  describe('updateCategorySchema', () => {
    it('validates partial update data', () => {
      const validData = {
        name: 'Updated Electronics'
      }

      const result = updateCategorySchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe('Updated Electronics')
      }
    })

    it('validates empty object', () => {
      const validData = {}

      const result = updateCategorySchema.safeParse(validData)
      expect(result.success).toBe(true)
    })
  })

  describe('createProductSchema', () => {
    it('validates valid product data', () => {
      const validData = {
        name: 'Smartphone',
        categoryId: 1,
        packagingDescription: 'Retail box',
        pricePerPackage: 599.99,
        unitsPerPackage: 1,
        packagingTypeId: 1,
        sku: 'PHONE001',
        barcode: '1234567890123',
        description: 'Latest smartphone model'
      }

      const result = createProductSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe('Smartphone')
        expect(result.data.categoryId).toBe(1)
        expect(result.data.pricePerPackage).toBe(599.99)
      }
    })

    it('validates product with minimum required fields', () => {
      const validData = {
        name: 'Basic Product',
        categoryId: 1,
        packagingDescription: 'Simple package',
        pricePerPackage: 10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1
      }

      const result = createProductSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('rejects negative price', () => {
      const invalidData = {
        name: 'Product',
        categoryId: 1,
        packagingDescription: 'Package',
        pricePerPackage: -10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1
      }

      const result = createProductSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than 0')
      }
    })

    it('rejects zero units per package', () => {
      const invalidData = {
        name: 'Product',
        categoryId: 1,
        packagingDescription: 'Package',
        pricePerPackage: 10.00,
        unitsPerPackage: 0,
        packagingTypeId: 1
      }

      const result = createProductSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than 0')
      }
    })

    it('rejects invalid barcode format', () => {
      const invalidData = {
        name: 'Product',
        categoryId: 1,
        packagingDescription: 'Package',
        pricePerPackage: 10.00,
        unitsPerPackage: 1,
        packagingTypeId: 1,
        barcode: '123' // Too short
      }

      const result = createProductSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 8 characters')
      }
    })
  })

  describe('createSalesTransactionSchema', () => {
    it('validates valid transaction data', () => {
      const validData = {
        items: [
          {
            productId: 1,
            quantity: 2,
            unitPrice: 10.00
          },
          {
            productId: 2,
            quantity: 1,
            unitPrice: 5.00
          }
        ],
        paymentMethod: 'CASH',
        paymentReceived: 30.00,
        discountAmount: 5.00,
        notes: 'Customer discount applied'
      }

      const result = createSalesTransactionSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.items).toHaveLength(2)
        expect(result.data.paymentMethod).toBe('CASH')
        expect(result.data.paymentReceived).toBe(30.00)
      }
    })

    it('validates transaction with minimum required fields', () => {
      const validData = {
        items: [
          {
            productId: 1,
            quantity: 1,
            unitPrice: 10.00
          }
        ],
        paymentMethod: 'CARD',
        paymentReceived: 10.00
      }

      const result = createSalesTransactionSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('rejects empty items array', () => {
      const invalidData = {
        items: [],
        paymentMethod: 'CASH',
        paymentReceived: 10.00
      }

      const result = createSalesTransactionSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 1 element')
      }
    })

    it('rejects invalid payment method', () => {
      const invalidData = {
        items: [
          {
            productId: 1,
            quantity: 1,
            unitPrice: 10.00
          }
        ],
        paymentMethod: 'BITCOIN',
        paymentReceived: 10.00
      }

      const result = createSalesTransactionSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid enum value')
      }
    })

    it('rejects negative quantities', () => {
      const invalidData = {
        items: [
          {
            productId: 1,
            quantity: -1,
            unitPrice: 10.00
          }
        ],
        paymentMethod: 'CASH',
        paymentReceived: 10.00
      }

      const result = createSalesTransactionSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than 0')
      }
    })
  })

  describe('updateInventorySchema', () => {
    it('validates valid inventory update', () => {
      const validData = {
        currentStock: 100,
        minimumStock: 10,
        maximumStock: 500
      }

      const result = updateInventorySchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.currentStock).toBe(100)
        expect(result.data.minimumStock).toBe(10)
        expect(result.data.maximumStock).toBe(500)
      }
    })

    it('validates partial inventory update', () => {
      const validData = {
        currentStock: 50
      }

      const result = updateInventorySchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('rejects negative stock values', () => {
      const invalidData = {
        currentStock: -10
      }

      const result = updateInventorySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than or equal to 0')
      }
    })
  })

  describe('paginationSchema', () => {
    it('validates valid pagination parameters', () => {
      const validData = {
        page: 2,
        limit: 50,
        search: 'test',
        sortBy: 'name',
        sortOrder: 'desc'
      }

      const result = paginationSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.page).toBe(2)
        expect(result.data.limit).toBe(50)
        expect(result.data.sortOrder).toBe('desc')
      }
    })

    it('applies default values', () => {
      const validData = {}

      const result = paginationSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.page).toBe(1)
        expect(result.data.limit).toBe(20)
        expect(result.data.sortOrder).toBe('asc')
      }
    })

    it('rejects invalid page number', () => {
      const invalidData = {
        page: 0
      }

      const result = paginationSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('greater than or equal to 1')
      }
    })

    it('rejects limit exceeding maximum', () => {
      const invalidData = {
        limit: 1001
      }

      const result = paginationSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('less than or equal to 1000')
      }
    })
  })
})
