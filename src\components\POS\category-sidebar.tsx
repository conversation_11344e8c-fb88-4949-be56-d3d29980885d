'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { Category } from '@/types'

interface CategorySidebarProps {
  isOpen: boolean
  categories: Category[]
  selectedCategory: number | null
  onSelectCategory: (categoryId: number | null) => void
}

export function CategorySidebar({
  isOpen,
  categories,
  selectedCategory,
  onSelectCategory
}: CategorySidebarProps) {
  if (!isOpen) return null

  return (
    <div className="w-64 bg-white shadow-sm border-r overflow-y-auto">
      <Card className="border-0 rounded-none">
        <CardHeader>
          <CardTitle className="text-lg">Categories</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button
            variant={selectedCategory === null ? "default" : "ghost"}
            className={cn(
              "w-full justify-start",
              selectedCategory === null && "bg-primary text-primary-foreground"
            )}
            onClick={() => onSelectCategory(null)}
          >
            All Products
          </Button>
          
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "ghost"}
              className={cn(
                "w-full justify-start",
                selectedCategory === category.id && "bg-primary text-primary-foreground"
              )}
              onClick={() => onSelectCategory(category.id)}
            >
              {category.name}
            </Button>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
