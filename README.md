# POS Arta - Modern Point of Sale Application

A comprehensive Point of Sale (POS) application built with **Next.js 15**, **Prisma ORM**, **shadcn/ui**, and **Zod validation**. This modern application provides a complete POS system for retail operations with type-safe database operations, beautiful UI components, and robust form validation.

## Features

### ✅ Completed Features

- **Modern Architecture**: Refactored to use Prisma ORM, shadcn/ui, and Zod validation
- **Database Migration**: Automated migration from `product.json` to PostgreSQL with Prisma
- **Type-Safe Operations**: Full type safety from database to UI with Prisma and Zod
- **Modern UI Components**: Beautiful, accessible components using shadcn/ui
- **POS Interface**: Responsive cashier interface with modern design
- **Sales Transactions**: Complete checkout process with receipt generation
- **Inventory Tracking**: Real-time stock management with validation
- **Category Management**: Organized product categorization

### 🚧 In Development

- User authentication and authorization
- Admin dashboard for product/category management
- Advanced inventory alerts and reporting
- Sales analytics dashboard
- Barcode scanning integration
- Multi-user support with role-based access

## Modern Technology Stack

- **Frontend**: Next.js 15 (App Router), React 19, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **UI Framework**: shadcn/ui components with Tailwind CSS v4
- **Validation**: Zod for type-safe form validation
- **Forms**: React Hook Form with Zod resolvers
- **Icons**: Lucide React icons
- **State Management**: React hooks and server state

## 🚀 Modern Refactoring

This application has been completely refactored to use modern development tools and patterns:

### Database Layer Modernization

- **Replaced raw SQL** with Prisma ORM for type-safe database operations
- **Generated Prisma Client** provides full TypeScript support
- **Database schema** defined in `prisma/schema.prisma` with relationships
- **Automatic migrations** and database introspection

### UI Component Modernization

- **Replaced custom components** with shadcn/ui for consistency and accessibility
- **Tailwind CSS v4** with CSS variables for theming
- **Radix UI primitives** for robust, accessible component foundations
- **Lucide React icons** replacing Heroicons for better tree-shaking

### Validation & Forms

- **Zod schemas** for runtime validation and TypeScript inference
- **React Hook Form** integration with Zod resolvers
- **Type-safe API endpoints** with request/response validation
- **Client and server-side validation** consistency

### Architecture Improvements

- **Next.js 15 App Router** structure (`app/` directory)
- **Server Components** and Client Components separation
- **API Routes** with proper error handling and validation
- **TypeScript strict mode** with full type coverage

## Database Schema

### Tables

1. **categories** - Product categories (Candy, Makanan, Minuman, Personal Care)
2. **packaging_types** - Packaging unit types (sak, pcs, box, etc.)
3. **products** - Product information with normalized relationships
4. **inventory** - Stock levels and inventory management
5. **users** - System users (admin, cashier, manager)
6. **sales_transactions** - Transaction records
7. **sales_transaction_items** - Transaction line items
8. **stock_movements** - Inventory movement history

## Installation and Setup

### Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL 12+
- Git

### 1. Clone the Repository

```bash
git clone <repository-url>
cd pos-arta
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Database Setup

#### Create PostgreSQL Database

```sql
CREATE DATABASE pos_arta;
CREATE USER pos_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE pos_arta TO pos_user;
```

#### Set up Environment Variables

```bash
cp .env.example .env.local
```

Edit `.env.local` with your database credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pos_arta
DB_USER=pos_user
DB_PASSWORD=your_password

# Prisma Database URL
DATABASE_URL="postgresql://pos_user:your_password@localhost:5432/pos_arta?schema=public"
```

#### Initialize Database with Prisma

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or run migrations (for production)
npm run db:migrate
```

#### Migrate Product Data

```bash
npm run migrate
```

### 4. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## API Endpoints

### Categories

- `GET /api/categories` - Get all categories with pagination
- `POST /api/categories` - Create new category
- `GET /api/categories/[id]` - Get category by ID
- `PUT /api/categories/[id]` - Update category
- `DELETE /api/categories/[id]` - Delete category

### Products

- `GET /api/products` - Get all products with filters and pagination
- `POST /api/products` - Create new product
- `GET /api/products/[id]` - Get product by ID
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Soft delete product

## Usage

### POS Interface

1. Navigate to `/pos` for the cashier interface
2. Browse products by category or search
3. Add products to cart by clicking on them
4. Adjust quantities in the cart
5. Proceed to checkout (in development)

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run migrate` - Run data migration
- `npm run db:setup` - Initialize database schema
- `npm run test` - Run tests (planned)
- `npm run lint` - Run ESLint

## Data Migration

The application includes automated migration from the original `product.json` file:

### Original Data Structure

- 77 products across 4 categories
- Mixed packaging types and units
- Pricing in Indonesian Rupiah (IDR)

### Normalized Structure

- **Categories**: 4 normalized categories
- **Packaging Types**: 10 standardized packaging units
- **Products**: Full product information with relationships
- **Inventory**: Initial stock levels for all products

## License

This project is licensed under the MIT License.
