# POS Arta - Point of Sale Application

A comprehensive Point of Sale (POS) application built with Next.js, TypeScript, PostgreSQL, and Tailwind CSS. This application migrates product data from JSON to a normalized PostgreSQL database and provides a complete POS system for retail operations.

## Features

### ✅ Completed Features

- **Database Migration**: Automated migration from `product.json` to PostgreSQL
- **Normalized Database Schema**: Separate tables for categories, products, inventory, and transactions
- **Product Management**: CRUD operations for products and categories
- **POS Interface**: Modern, responsive cashier interface
- **Inventory Tracking**: Real-time stock management
- **Category Management**: Organized product categorization

### 🚧 In Development

- Sales transaction processing
- Receipt generation
- User authentication and authorization
- Admin dashboard
- Inventory alerts and reporting
- Sales analytics

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: PostgreSQL
- **UI Components**: Heroicons, Custom components
- **State Management**: React hooks, Zustand (planned)

## Database Schema

### Tables

1. **categories** - Product categories (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Personal Care)
2. **packaging_types** - Packaging unit types (sak, pcs, box, etc.)
3. **products** - Product information with normalized relationships
4. **inventory** - Stock levels and inventory management
5. **users** - System users (admin, cashier, manager)
6. **sales_transactions** - Transaction records
7. **sales_transaction_items** - Transaction line items
8. **stock_movements** - Inventory movement history

## Installation and Setup

### Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL 12+
- Git

### 1. Clone the Repository

```bash
git clone <repository-url>
cd pos-arta
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Database Setup

#### Create PostgreSQL Database

```sql
CREATE DATABASE pos_arta;
CREATE USER pos_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE pos_arta TO pos_user;
```

#### Set up Environment Variables

```bash
cp .env.example .env.local
```

Edit `.env.local` with your database credentials:

```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pos_arta
DB_USER=pos_user
DB_PASSWORD=your_password
```

#### Initialize Database Schema

```bash
npm run db:setup
```

#### Migrate Product Data

```bash
npm run migrate
```

### 4. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## API Endpoints

### Categories

- `GET /api/categories` - Get all categories with pagination
- `POST /api/categories` - Create new category
- `GET /api/categories/[id]` - Get category by ID
- `PUT /api/categories/[id]` - Update category
- `DELETE /api/categories/[id]` - Delete category

### Products

- `GET /api/products` - Get all products with filters and pagination
- `POST /api/products` - Create new product
- `GET /api/products/[id]` - Get product by ID
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Soft delete product

## Usage

### POS Interface

1. Navigate to `/pos` for the cashier interface
2. Browse products by category or search
3. Add products to cart by clicking on them
4. Adjust quantities in the cart
5. Proceed to checkout (in development)

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run migrate` - Run data migration
- `npm run db:setup` - Initialize database schema
- `npm run test` - Run tests (planned)
- `npm run lint` - Run ESLint

## Data Migration

The application includes automated migration from the original `product.json` file:

### Original Data Structure

- 77 products across 4 categories
- Mixed packaging types and units
- Pricing in Indonesian Rupiah (IDR)

### Normalized Structure

- **Categories**: 4 normalized categories
- **Packaging Types**: 10 standardized packaging units
- **Products**: Full product information with relationships
- **Inventory**: Initial stock levels for all products

## License

This project is licensed under the MIT License.
