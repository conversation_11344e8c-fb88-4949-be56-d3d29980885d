import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`
    
    // Get basic system info
    const [
      categoriesCount,
      productsCount,
      transactionsCount
    ] = await Promise.all([
      prisma.category.count(),
      prisma.product.count(),
      prisma.salesTransaction.count()
    ])
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: {
        status: 'connected',
        categories: categoriesCount,
        products: productsCount,
        transactions: transactionsCount
      },
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    }
    
    return NextResponse.json({
      success: true,
      data: health
    })
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      success: false,
      status: 'unhealthy',
      error: 'Database connection failed',
      timestamp: new Date().toISOString()
    }, { status: 503 })
  }
}

// Detailed health check
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { detailed = false } = body
    
    if (!detailed) {
      // Return basic health check
      return GET(request)
    }
    
    // Perform detailed checks
    const checks = {
      database: { status: 'unknown', latency: 0 },
      api: { status: 'unknown', endpoints: 0 },
      memory: { status: 'unknown', usage: 0 },
      disk: { status: 'unknown', available: 0 }
    }
    
    // Database check with latency
    const dbStart = Date.now()
    try {
      await prisma.$queryRaw`SELECT 1`
      checks.database.status = 'healthy'
      checks.database.latency = Date.now() - dbStart
    } catch (error) {
      checks.database.status = 'unhealthy'
    }
    
    // Memory check
    const memUsage = process.memoryUsage()
    const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100
    checks.memory.status = memoryUsagePercent > 90 ? 'warning' : 'healthy'
    checks.memory.usage = Math.round(memoryUsagePercent)
    
    // API endpoints check (basic count)
    checks.api.status = 'healthy'
    checks.api.endpoints = 8 // Current number of API endpoints
    
    // Overall status
    const overallStatus = Object.values(checks).every(check => 
      check.status === 'healthy' || check.status === 'warning'
    ) ? 'healthy' : 'unhealthy'
    
    return NextResponse.json({
      success: true,
      data: {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        checks,
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime(),
          pid: process.pid
        }
      }
    })
  } catch (error) {
    console.error('Detailed health check failed:', error)
    
    return NextResponse.json({
      success: false,
      status: 'unhealthy',
      error: 'Health check failed',
      timestamp: new Date().toISOString()
    }, { status: 503 })
  }
}
