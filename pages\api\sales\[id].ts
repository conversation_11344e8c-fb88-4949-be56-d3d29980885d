import { NextApiRequest, NextApiResponse } from 'next';
import { SalesTransactionModel } from '../../../models/SalesTransaction';
import { ApiResponse } from '../../../types';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { id } = req.query;
  const transactionId = parseInt(id as string);

  if (isNaN(transactionId)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid transaction ID'
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGet(transactionId, res);
        break;
      case 'PUT':
        await handlePut(transactionId, req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).json({
          success: false,
          error: `Method ${req.method} Not Allowed`
        });
    }
  } catch (error) {
    console.error('Transaction API error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

async function handleGet(transactionId: number, res: NextApiResponse<ApiResponse>) {
  try {
    const transaction = await SalesTransactionModel.getById(transactionId);
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    res.status(200).json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch transaction'
    });
  }
}

async function handlePut(
  transactionId: number,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { status } = req.body;

  // Validation
  if (!status || !['pending', 'completed', 'cancelled', 'refunded'].includes(status)) {
    return res.status(400).json({
      success: false,
      error: 'Valid status is required (pending, completed, cancelled, or refunded)'
    });
  }

  try {
    const transaction = await SalesTransactionModel.updateStatus(transactionId, status);
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    res.status(200).json({
      success: true,
      data: transaction,
      message: 'Transaction status updated successfully'
    });
  } catch (error) {
    console.error('Error updating transaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update transaction'
    });
  }
}
