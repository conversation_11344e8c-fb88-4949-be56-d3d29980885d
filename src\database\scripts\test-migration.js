#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Starting POS Arta Migration Test Suite...\n')

// Test configuration
const testConfig = {
  unit: {
    name: 'Unit Tests',
    pattern: '__tests__/**/*.test.{ts,tsx}',
    exclude: '__tests__/integration/**/*'
  },
  integration: {
    name: 'Integration Tests',
    pattern: '__tests__/integration/**/*.test.{ts,tsx}'
  },
  api: {
    name: 'API Tests',
    pattern: '__tests__/api/**/*.test.{ts,tsx}'
  },
  components: {
    name: 'Component Tests',
    pattern: '__tests__/components/**/*.test.{tsx}'
  }
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`
}

function runTestSuite(suiteName, config) {
  console.log(colorize(`\n📋 Running ${config.name}...`, 'blue'))
  console.log(colorize('─'.repeat(50), 'cyan'))
  
  try {
    let command = `npx jest "${config.pattern}"`
    
    if (config.exclude) {
      command += ` --testPathIgnorePatterns="${config.exclude}"`
    }
    
    command += ' --verbose --coverage --coverageReporters=text-summary'
    
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    })
    
    console.log(output)
    console.log(colorize(`✅ ${config.name} completed successfully!`, 'green'))
    return { success: true, output }
    
  } catch (error) {
    console.log(colorize(`❌ ${config.name} failed!`, 'red'))
    console.log(error.stdout || error.message)
    return { success: false, error: error.message }
  }
}

function generateTestReport(results) {
  console.log(colorize('\n📊 Test Results Summary', 'magenta'))
  console.log(colorize('═'.repeat(60), 'magenta'))
  
  let totalPassed = 0
  let totalFailed = 0
  
  Object.entries(results).forEach(([suite, result]) => {
    const status = result.success ? 
      colorize('✅ PASSED', 'green') : 
      colorize('❌ FAILED', 'red')
    
    console.log(`${testConfig[suite].name}: ${status}`)
    
    if (result.success) {
      totalPassed++
    } else {
      totalFailed++
    }
  })
  
  console.log(colorize('\n📈 Overall Results:', 'bright'))
  console.log(`Total Test Suites: ${totalPassed + totalFailed}`)
  console.log(`Passed: ${colorize(totalPassed, 'green')}`)
  console.log(`Failed: ${colorize(totalFailed, 'red')}`)
  
  const overallStatus = totalFailed === 0 ? 'PASSED' : 'FAILED'
  const statusColor = totalFailed === 0 ? 'green' : 'red'
  
  console.log(`\nOverall Status: ${colorize(overallStatus, statusColor)}`)
  
  return totalFailed === 0
}

function checkPrerequisites() {
  console.log(colorize('🔍 Checking prerequisites...', 'yellow'))
  
  // Check if Jest is installed
  try {
    execSync('npx jest --version', { stdio: 'pipe' })
    console.log(colorize('✅ Jest is installed', 'green'))
  } catch (error) {
    console.log(colorize('❌ Jest is not installed', 'red'))
    console.log('Please run: npm install --save-dev jest @types/jest')
    process.exit(1)
  }
  
  // Check if test files exist
  const testDirs = ['__tests__', '__tests__/api', '__tests__/components', '__tests__/integration']
  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(colorize(`✅ ${dir} directory exists`, 'green'))
    } else {
      console.log(colorize(`⚠️  ${dir} directory not found`, 'yellow'))
    }
  })
  
  // Check if Jest config exists
  if (fs.existsSync('jest.config.js')) {
    console.log(colorize('✅ Jest configuration found', 'green'))
  } else {
    console.log(colorize('⚠️  Jest configuration not found', 'yellow'))
  }
  
  console.log('')
}

function validateMigration() {
  console.log(colorize('🔧 Validating Migration Components...', 'cyan'))
  
  const criticalFiles = [
    'lib/models/category.ts',
    'lib/models/product.ts',
    'lib/models/sales-transaction.ts',
    'lib/models/inventory.ts',
    'lib/validations.ts',
    'lib/prisma.ts',
    'app/api/categories/route.ts',
    'app/api/products/route.ts',
    'app/api/sales/route.ts',
    'components/pos/product-grid.tsx',
    'components/pos/cart.tsx',
    'components/pos/checkout-dialog.tsx'
  ]
  
  let missingFiles = []
  
  criticalFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(colorize(`✅ ${file}`, 'green'))
    } else {
      console.log(colorize(`❌ ${file} - MISSING`, 'red'))
      missingFiles.push(file)
    }
  })
  
  if (missingFiles.length > 0) {
    console.log(colorize(`\n⚠️  Warning: ${missingFiles.length} critical files are missing!`, 'yellow'))
    console.log('Migration may be incomplete.')
  } else {
    console.log(colorize('\n✅ All critical migration files are present!', 'green'))
  }
  
  console.log('')
  return missingFiles.length === 0
}

async function main() {
  console.log(colorize('POS Arta - Migration Test Suite', 'bright'))
  console.log(colorize('Modern Stack Validation & Testing', 'cyan'))
  console.log(colorize('═'.repeat(60), 'cyan'))
  
  // Check prerequisites
  checkPrerequisites()
  
  // Validate migration
  const migrationValid = validateMigration()
  
  if (!migrationValid) {
    console.log(colorize('❌ Migration validation failed. Please ensure all files are present.', 'red'))
    process.exit(1)
  }
  
  // Run test suites
  const results = {}
  
  for (const [suiteName, config] of Object.entries(testConfig)) {
    results[suiteName] = runTestSuite(suiteName, config)
  }
  
  // Generate report
  const allTestsPassed = generateTestReport(results)
  
  // Final status
  console.log(colorize('\n🎯 Migration Test Summary:', 'bright'))
  console.log(colorize('─'.repeat(40), 'cyan'))
  
  if (allTestsPassed) {
    console.log(colorize('🎉 All tests passed! Migration is successful.', 'green'))
    console.log(colorize('✅ POS Arta is ready for production use.', 'green'))
    process.exit(0)
  } else {
    console.log(colorize('❌ Some tests failed. Please review and fix issues.', 'red'))
    console.log(colorize('🔧 Check the test output above for details.', 'yellow'))
    process.exit(1)
  }
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.log(colorize(`\n💥 Uncaught Exception: ${error.message}`, 'red'))
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.log(colorize(`\n💥 Unhandled Rejection: ${reason}`, 'red'))
  process.exit(1)
})

// Run the test suite
main().catch(error => {
  console.log(colorize(`\n💥 Test suite failed: ${error.message}`, 'red'))
  process.exit(1)
})
