// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Category {
  id          Int       @id @default(autoincrement())
  name        String    @unique @db.VarChar(100)
  description String?   @db.Text
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  // Relations
  products    Product[]

  @@map("categories")
}

model PackagingType {
  id          Int       @id @default(autoincrement())
  name        String    @unique @db.VarChar(50)
  description String?   @db.Text
  createdAt   DateTime  @default(now()) @map("created_at")
  
  // Relations
  products    Product[]

  @@map("packaging_types")
}

model Product {
  id                   Int           @id @default(autoincrement())
  name                 String        @db.VarChar(255)
  categoryId           Int           @map("category_id")
  packagingDescription String        @map("packaging_description") @db.VarChar(255)
  pricePerPackage      Decimal       @map("price_per_package") @db.Decimal(12, 2)
  unitsPerPackage      Int           @map("units_per_package")
  packagingTypeId      Int           @map("packaging_type_id")
  unitPrice            Decimal?      @map("unit_price") @db.Decimal(12, 2)
  sku                  String?       @unique @db.VarChar(100)
  barcode              String?       @unique @db.VarChar(100)
  description          String?       @db.Text
  isActive             Boolean       @default(true) @map("is_active")
  createdAt            DateTime      @default(now()) @map("created_at")
  updatedAt            DateTime      @updatedAt @map("updated_at")
  
  // Relations
  category             Category      @relation(fields: [categoryId], references: [id], onDelete: Restrict)
  packagingType        PackagingType @relation(fields: [packagingTypeId], references: [id], onDelete: Restrict)
  inventory            Inventory?
  transactionItems     SalesTransactionItem[]
  stockMovements       StockMovement[]

  @@map("products")
}

model Inventory {
  id               Int       @id @default(autoincrement())
  productId        Int       @unique @map("product_id")
  currentStock     Int       @default(0) @map("current_stock")
  minimumStock     Int       @default(10) @map("minimum_stock")
  maximumStock     Int       @default(1000) @map("maximum_stock")
  lastRestockedAt  DateTime? @map("last_restocked_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  
  // Relations
  product          Product   @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("inventory")
}

model User {
  id           Int       @id @default(autoincrement())
  username     String    @unique @db.VarChar(50)
  email        String?   @unique @db.VarChar(255)
  passwordHash String    @map("password_hash") @db.VarChar(255)
  fullName     String    @map("full_name") @db.VarChar(255)
  role         UserRole  @default(CASHIER)
  isActive     Boolean   @default(true) @map("is_active")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  
  // Relations
  transactions SalesTransaction[]
  stockMovements StockMovement[]

  @@map("users")
}

model SalesTransaction {
  id              Int                    @id @default(autoincrement())
  transactionNumber String               @unique @map("transaction_number") @db.VarChar(50)
  userId          Int                    @map("user_id")
  totalAmount     Decimal                @map("total_amount") @db.Decimal(12, 2)
  taxAmount       Decimal                @default(0) @map("tax_amount") @db.Decimal(12, 2)
  discountAmount  Decimal                @default(0) @map("discount_amount") @db.Decimal(12, 2)
  finalAmount     Decimal                @map("final_amount") @db.Decimal(12, 2)
  paymentMethod   PaymentMethod          @map("payment_method")
  paymentReceived Decimal                @map("payment_received") @db.Decimal(12, 2)
  changeAmount    Decimal                @default(0) @map("change_amount") @db.Decimal(12, 2)
  status          TransactionStatus      @default(COMPLETED)
  notes           String?                @db.Text
  createdAt       DateTime               @default(now()) @map("created_at")
  
  // Relations
  user            User                   @relation(fields: [userId], references: [id], onDelete: Restrict)
  items           SalesTransactionItem[]

  @@map("sales_transactions")
}

model SalesTransactionItem {
  id            Int              @id @default(autoincrement())
  transactionId Int              @map("transaction_id")
  productId     Int              @map("product_id")
  quantity      Int
  unitPrice     Decimal          @map("unit_price") @db.Decimal(12, 2)
  totalPrice    Decimal          @map("total_price") @db.Decimal(12, 2)
  createdAt     DateTime         @default(now()) @map("created_at")
  
  // Relations
  transaction   SalesTransaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  product       Product          @relation(fields: [productId], references: [id], onDelete: Restrict)

  @@map("sales_transaction_items")
}

model StockMovement {
  id            Int               @id @default(autoincrement())
  productId     Int               @map("product_id")
  movementType  MovementType      @map("movement_type")
  quantity      Int
  referenceType ReferenceType?    @map("reference_type")
  referenceId   Int?              @map("reference_id")
  notes         String?           @db.Text
  userId        Int?              @map("user_id")
  createdAt     DateTime          @default(now()) @map("created_at")
  
  // Relations
  product       Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
  user          User?             @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("stock_movements")
}

// Enums
enum UserRole {
  ADMIN
  CASHIER
  MANAGER

  @@map("user_role")
}

enum PaymentMethod {
  CASH
  CARD
  DIGITAL

  @@map("payment_method")
}

enum TransactionStatus {
  PENDING
  COMPLETED
  CANCELLED
  REFUNDED

  @@map("transaction_status")
}

enum MovementType {
  IN
  OUT
  ADJUSTMENT

  @@map("movement_type")
}

enum ReferenceType {
  SALE
  RESTOCK
  ADJUSTMENT
  RETURN

  @@map("reference_type")
}
