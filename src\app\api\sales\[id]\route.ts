import { NextRequest, NextResponse } from 'next/server'
import { SalesTransactionService } from '@/lib/models/sales-transaction'
import { updateTransactionStatusSchema } from '@/lib/validations'
import { z } from 'zod'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const transactionId = parseInt(params.id)
    
    if (isNaN(transactionId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid transaction ID'
      }, { status: 400 })
    }
    
    const transaction = await SalesTransactionService.getById(transactionId)
    
    if (!transaction) {
      return NextResponse.json({
        success: false,
        error: 'Transaction not found'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      data: transaction
    })
  } catch (error) {
    console.error('Error fetching transaction:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch transaction'
    }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const transactionId = parseInt(params.id)
    
    if (isNaN(transactionId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid transaction ID'
      }, { status: 400 })
    }
    
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateTransactionStatusSchema.parse(body)
    
    // Check if transaction exists
    const existingTransaction = await SalesTransactionService.getById(transactionId)
    if (!existingTransaction) {
      return NextResponse.json({
        success: false,
        error: 'Transaction not found'
      }, { status: 404 })
    }
    
    // Check if status change is valid
    if (existingTransaction.status === 'COMPLETED' && validatedData.status !== 'REFUNDED') {
      return NextResponse.json({
        success: false,
        error: 'Cannot change status of completed transaction except to refunded'
      }, { status: 400 })
    }
    
    if (existingTransaction.status === 'CANCELLED') {
      return NextResponse.json({
        success: false,
        error: 'Cannot change status of cancelled transaction'
      }, { status: 400 })
    }
    
    const updatedTransaction = await SalesTransactionService.updateStatus(
      transactionId, 
      validatedData.status,
      validatedData.notes
    )
    
    return NextResponse.json({
      success: true,
      data: updatedTransaction,
      message: 'Transaction status updated successfully'
    })
  } catch (error) {
    console.error('Error updating transaction status:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to update transaction status'
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const transactionId = parseInt(params.id)
    
    if (isNaN(transactionId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid transaction ID'
      }, { status: 400 })
    }
    
    // Check if transaction exists
    const existingTransaction = await SalesTransactionService.getById(transactionId)
    if (!existingTransaction) {
      return NextResponse.json({
        success: false,
        error: 'Transaction not found'
      }, { status: 404 })
    }
    
    // Only allow deletion of pending or cancelled transactions
    if (existingTransaction.status === 'COMPLETED') {
      return NextResponse.json({
        success: false,
        error: 'Cannot delete completed transaction. Use refund instead.'
      }, { status: 400 })
    }
    
    await SalesTransactionService.delete(transactionId)
    
    return NextResponse.json({
      success: true,
      message: 'Transaction deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting transaction:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to delete transaction'
    }, { status: 500 })
  }
}
