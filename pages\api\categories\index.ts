import { NextApiRequest, NextApiResponse } from 'next';
import { CategoryModel } from '../../../models/Category';
import { ApiResponse, CreateCategoryRequest, PaginationParams } from '../../../types';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res);
        break;
      case 'POST':
        await handlePost(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).json({
          success: false,
          error: `Method ${req.method} Not Allowed`
        });
    }
  } catch (error) {
    console.error('Categories API error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
  const {
    page = '1',
    limit = '50',
    search = '',
    sort_by = 'name',
    sort_order = 'asc'
  } = req.query;

  const params: PaginationParams = {
    page: parseInt(page as string),
    limit: parseInt(limit as string),
    search: search as string,
    sort_by: sort_by as string,
    sort_order: sort_order as 'asc' | 'desc'
  };

  try {
    const result = await CategoryModel.getAll(params);
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch categories'
    });
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse<ApiResponse>) {
  const { name, description }: CreateCategoryRequest = req.body;

  // Validation
  if (!name || name.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Category name is required'
    });
  }

  if (name.length > 100) {
    return res.status(400).json({
      success: false,
      error: 'Category name must be 100 characters or less'
    });
  }

  try {
    // Check if category name already exists
    const existingCategory = await CategoryModel.getByName(name.trim());
    if (existingCategory) {
      return res.status(409).json({
        success: false,
        error: 'Category name already exists'
      });
    }

    const category = await CategoryModel.create({
      name: name.trim(),
      description: description?.trim()
    });

    res.status(201).json({
      success: true,
      data: category,
      message: 'Category created successfully'
    });
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create category'
    });
  }
}
