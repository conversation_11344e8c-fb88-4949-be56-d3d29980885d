import { prisma } from '@/lib/prisma'
import type { Inventory, StockMovement, Prisma } from '@prisma/client'

export interface InventoryWithProduct extends Inventory {
  product: {
    id: number
    name: string
    sku: string | null
    category: {
      name: string
    }
  }
}

export interface InventoryAlert {
  productId: number
  productName: string
  currentStock: number
  minimumStock: number
  status: 'low_stock' | 'out_of_stock'
}

export interface PaginatedInventoryResponse {
  data: InventoryWithProduct[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface InventoryFilters {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  lowStock?: boolean
  outOfStock?: boolean
}

export class InventoryService {
  static async getAll(filters: InventoryFilters): Promise<PaginatedInventoryResponse> {
    const {
      page = 1,
      limit = 20,
      search = '',
      sortBy = 'product.name',
      sortOrder = 'asc',
      lowStock = false,
      outOfStock = false
    } = filters

    const skip = (page - 1) * limit

    // Build where clause
    const where: Prisma.InventoryWhereInput = {}

    if (search) {
      where.product = {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { sku: { contains: search, mode: 'insensitive' } }
        ]
      }
    }

    if (lowStock) {
      where.currentStock = {
        lte: prisma.inventory.fields.minimumStock
      }
    }

    if (outOfStock) {
      where.currentStock = 0
    }

    // Build order by clause
    const orderBy: Prisma.InventoryOrderByWithRelationInput = {}
    if (sortBy.includes('.')) {
      const [relation, field] = sortBy.split('.')
      if (relation === 'product') {
        orderBy.product = { [field]: sortOrder }
      }
    } else {
      orderBy[sortBy as keyof Inventory] = sortOrder
    }

    const [data, totalItems] = await Promise.all([
      prisma.inventory.findMany({
        where,
        include: {
          product: {
            include: {
              category: {
                select: { name: true }
              }
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.inventory.count({ where })
    ])

    const totalPages = Math.ceil(totalItems / limit)

    return {
      data: data as InventoryWithProduct[],
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }

  static async getByProductId(productId: number): Promise<Inventory | null> {
    return prisma.inventory.findUnique({
      where: { productId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true
          }
        }
      }
    })
  }

  static async update(
    productId: number,
    data: Partial<Pick<Inventory, 'currentStock' | 'minimumStock' | 'maximumStock'>>
  ): Promise<Inventory> {
    return prisma.inventory.update({
      where: { productId },
      data: {
        ...data,
        updatedAt: new Date(),
        ...(data.currentStock !== undefined && { lastRestockedAt: new Date() })
      }
    })
  }

  static async adjustStock(
    productId: number,
    adjustment: number,
    reason: string,
    referenceType: 'MANUAL' | 'SALE' | 'PURCHASE' | 'ADJUSTMENT' = 'MANUAL',
    referenceId?: number
  ): Promise<StockMovement> {
    return prisma.$transaction(async (tx) => {
      // Get current inventory
      const inventory = await tx.inventory.findUnique({
        where: { productId }
      })

      if (!inventory) {
        throw new Error('Inventory record not found')
      }

      const newStock = inventory.currentStock + adjustment

      if (newStock < 0) {
        throw new Error('Insufficient stock for this adjustment')
      }

      // Update inventory
      await tx.inventory.update({
        where: { productId },
        data: {
          currentStock: newStock,
          updatedAt: new Date(),
          ...(adjustment > 0 && { lastRestockedAt: new Date() })
        }
      })

      // Create stock movement record
      const stockMovement = await tx.stockMovement.create({
        data: {
          productId,
          movementType: adjustment > 0 ? 'IN' : 'OUT',
          quantity: Math.abs(adjustment),
          reason,
          referenceType,
          referenceId,
          balanceAfter: newStock
        }
      })

      return stockMovement
    })
  }

  static async getAlerts(type: 'low_stock' | 'out_of_stock' | 'all'): Promise<InventoryAlert[]> {
    const where: Prisma.InventoryWhereInput = {}

    if (type === 'low_stock') {
      where.AND = [
        { currentStock: { gt: 0 } },
        { currentStock: { lte: prisma.inventory.fields.minimumStock } }
      ]
    } else if (type === 'out_of_stock') {
      where.currentStock = 0
    } else if (type === 'all') {
      where.OR = [
        { currentStock: 0 },
        { currentStock: { lte: prisma.inventory.fields.minimumStock } }
      ]
    }

    const inventoryItems = await prisma.inventory.findMany({
      where,
      include: {
        product: {
          select: {
            name: true
          }
        }
      }
    })

    return inventoryItems.map(item => ({
      productId: item.productId,
      productName: item.product.name,
      currentStock: item.currentStock,
      minimumStock: item.minimumStock,
      status: item.currentStock === 0 ? 'out_of_stock' : 'low_stock'
    }))
  }

  static async getInventoryReport(filters?: {
    categoryId?: number
    lowStockOnly?: boolean
    outOfStockOnly?: boolean
  }): Promise<InventoryWithProduct[]> {
    const where: Prisma.InventoryWhereInput = {}

    if (filters?.categoryId) {
      where.product = {
        categoryId: filters.categoryId
      }
    }

    if (filters?.lowStockOnly) {
      where.AND = [
        { currentStock: { gt: 0 } },
        { currentStock: { lte: prisma.inventory.fields.minimumStock } }
      ]
    }

    if (filters?.outOfStockOnly) {
      where.currentStock = 0
    }

    const inventory = await prisma.inventory.findMany({
      where,
      include: {
        product: {
          include: {
            category: {
              select: { name: true }
            }
          }
        }
      },
      orderBy: {
        product: {
          name: 'asc'
        }
      }
    })

    return inventory as InventoryWithProduct[]
  }

  static async getStockMovements(
    productId: number,
    limit: number = 50
  ): Promise<StockMovement[]> {
    return prisma.stockMovement.findMany({
      where: { productId },
      orderBy: { createdAt: 'desc' },
      take: limit
    })
  }

  static async createInventoryRecord(
    productId: number,
    initialStock: number = 0,
    minimumStock: number = 0,
    maximumStock: number = 1000
  ): Promise<Inventory> {
    return prisma.inventory.create({
      data: {
        productId,
        currentStock: initialStock,
        minimumStock,
        maximumStock,
        lastRestockedAt: initialStock > 0 ? new Date() : null
      }
    })
  }

  static async deleteInventoryRecord(productId: number): Promise<void> {
    await prisma.inventory.delete({
      where: { productId }
    })
  }
}
