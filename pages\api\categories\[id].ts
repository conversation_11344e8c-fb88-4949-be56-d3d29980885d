import { NextApiRequest, NextApiResponse } from 'next';
import { CategoryModel } from '../../../models/Category';
import { ApiResponse, UpdateCategoryRequest } from '../../../types';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { id } = req.query;
  const categoryId = parseInt(id as string);

  if (isNaN(categoryId)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid category ID'
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGet(categoryId, res);
        break;
      case 'PUT':
        await handlePut(categoryId, req, res);
        break;
      case 'DELETE':
        await handleDelete(categoryId, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).json({
          success: false,
          error: `Method ${req.method} Not Allowed`
        });
    }
  } catch (error) {
    console.error('Category API error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

async function handleGet(categoryId: number, res: NextApiResponse<ApiResponse>) {
  try {
    const category = await CategoryModel.getById(categoryId);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch category'
    });
  }
}

async function handlePut(
  categoryId: number,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { name, description }: UpdateCategoryRequest = req.body;

  // Validation
  if (name !== undefined) {
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Category name cannot be empty'
      });
    }

    if (name.length > 100) {
      return res.status(400).json({
        success: false,
        error: 'Category name must be 100 characters or less'
      });
    }

    // Check if name already exists (excluding current category)
    const nameExists = await CategoryModel.nameExists(name.trim(), categoryId);
    if (nameExists) {
      return res.status(409).json({
        success: false,
        error: 'Category name already exists'
      });
    }
  }

  try {
    const updateData: UpdateCategoryRequest = {};
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim();

    const category = await CategoryModel.update(categoryId, updateData);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    res.status(200).json({
      success: true,
      data: category,
      message: 'Category updated successfully'
    });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update category'
    });
  }
}

async function handleDelete(categoryId: number, res: NextApiResponse<ApiResponse>) {
  try {
    const deleted = await CategoryModel.delete(categoryId);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    
    if (error instanceof Error && error.message.includes('Cannot delete category that has products')) {
      return res.status(409).json({
        success: false,
        error: 'Cannot delete category that has products. Please remove or reassign products first.'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete category'
    });
  }
}
