-- Data Migration Script for POS Application
-- Migrates data from product.json to PostgreSQL database

-- Insert categories (extracted from product data)
INSERT INTO categories (name, description) VALUES
('Candy', 'Sweet confectionery products and candies'),
('Makanan', 'Food products including snacks, wafers, and packaged foods'),
('Minuman', 'Beverages including water, tea, juice, and energy drinks'),
('Personal Care', 'Personal hygiene and care products including toothpaste and toothbrushes');

-- Insert packaging types (extracted from product data)
INSERT INTO packaging_types (name, description) VALUES
('sak', 'Sak/bag packaging'),
('pcs', 'Individual pieces'),
('box', 'Box packaging'),
('hgr', 'Hanging/display packaging'),
('ban', 'Band/strip packaging'),
('rcg', 'Renceng/bundle packaging'),
('can', 'Can packaging'),
('karton', 'Carton packaging'),
('lsn', 'Lusin/dozen packaging'),
('btl', 'Bottle packaging');

-- Insert products with normalized data
INSERT INTO products (name, category_id, packaging_description, price_per_package, units_per_package, packaging_type_id, sku) VALUES
-- Candy products
('Mintz 115gr', 1, '1 karton (24sak)', 134400, 24, 1, 'CANDY-001'),
('Blaster 125gr', 1, '1 karton (24sak)', 146400, 24, 1, 'CANDY-002'),
('Fruzz 100gr', 1, '1 karton (30sak)', 159000, 30, 2, 'CANDY-003'),
('Blaster Pop Assorted', 1, '1 karton (4hgr)', 60480, 4, 4, 'CANDY-004'),
('Fruzz balls 5.4 gr', 1, '1 karton (6 box)', 62640, 6, 3, 'CANDY-005'),
('Cannon Ball 5.4gr', 1, '1 karton (6box)', 62640, 6, 3, 'CANDY-006'),
('Cannon Ball 20gr', 1, '1 karton (6box)', 122400, 6, 3, 'CANDY-007'),
('Fruzz 500gr', 1, '1 karton (6sak)', 135000, 6, 1, 'CANDY-008'),

-- Makanan products
('Waffle Wa.HH', 2, '1 box (12pcs)', 20350, 12, 3, 'FOOD-001'),
('Tango wafer Cheese 98g', 2, '1 karton', 91500, 20, 8, 'FOOD-002'),
('Walut 15 gr', 2, '1 karton (10box/120pcs)', 103800, 120, 3, 'FOOD-003'),
('Oops Mini Puff BBQ 10gr', 2, '1 karton (120pcs)', 103800, 120, 5, 'FOOD-004'),
('Wafer Tango 16gr', 2, '1 karton (12ban)', 103800, 12, 5, 'FOOD-005'),
('Wafer Tango 5gr', 2, '1 karton (12box)', 102000, 12, 3, 'FOOD-006'),
('Oops 10gr', 2, '1 karton (12rcg)', 103800, 12, 6, 'FOOD-007'),
('Fugu 10gr', 2, '1 karton (12rcg)', 103800, 12, 6, 'FOOD-008'),
('Wafer Tango 99gr', 2, '1 karton (18pcs)', 139500, 18, 2, 'FOOD-009'),
('Imperial 162 gr', 2, '1 karton (20pcs)', 160000, 20, 2, 'FOOD-010'),
('Wafer Tango 100gr', 2, '1 karton (24pcs)', 110400, 24, 2, 'FOOD-011'),
('Wafer Tango 133gr', 2, '1 karton (24pcs)', 252000, 24, 2, 'FOOD-012'),
('Blastoz Bites 80gr', 2, '1 karton (24pcs)', 192000, 24, 2, 'FOOD-013'),
('Oops 40gr', 2, '1 karton (30pcs)', 99000, 30, 2, 'FOOD-014'),
('Wafer Tango 39 & 35gr', 2, '1 karton (6ban)', 97500, 6, 5, 'FOOD-015'),
('Fullo 7gr', 2, '1 karton (6box)', 62500, 6, 3, 'FOOD-016'),
('Blastoz 24gr', 2, '1 karton (6box)', 122250, 6, 3, 'FOOD-017'),
('Fullo 14gr', 2, '1 karton (6box/150pcs)', 121000, 150, 3, 'FOOD-018'),
('Tango wafer sealware 4g', 2, '1 karton (6pcs)', 102000, 6, 2, 'FOOD-019'),
('Fugu 20gr', 2, '1 karton (6rcg)', 102000, 6, 6, 'FOOD-020'),
('Fullo 25gr', 2, '1 karton (72pcs)', 122250, 72, 2, 'FOOD-021'),
('Waffle Tango 25gr', 2, '1 karton (8box)', 163000, 8, 3, 'FOOD-022'),
('Imperial 27gr', 2, '1 karton (8box)', 136000, 8, 3, 'FOOD-023'),
('Roppang panggang', 2, '1 pcs', 1650, 1, 2, 'FOOD-024'),

-- Minuman products
('Crystalin PET 1500ml', 3, '1 karton (12pcs)', 49344, 12, 2, 'DRINK-001'),
('Teh Gelas PET 350ml', 3, '1 karton (24btl)', 55992, 24, 2, 'DRINK-002'),
('Kiranti Ori 150ml', 3, '1 karton (24btl)', 121090, 24, 2, 'DRINK-003'),
('Kiranti Juice 150ml', 3, '1 karton (24btl)', 138040, 24, 2, 'DRINK-004'),
('Kiranti PL 120ml', 3, '1 karton (24btl)', 121090, 24, 2, 'DRINK-005'),
('Jagak Pegal Linu', 3, '1 karton (24btl)', 138040, 24, 2, 'DRINK-006'),
('You C Water 500ml', 3, '1 karton (24btl)', 154464, 24, 2, 'DRINK-007'),
('Redbull Gold Can 250ml', 3, '1 karton (24can)', 157176, 24, 7, 'DRINK-008'),
('Crystalin PET 330ml', 3, '1 karton (24pcs)', 35328, 24, 2, 'DRINK-009'),
('Crystalin PET 600ml', 3, '1 karton (24pcs)', 45216, 24, 2, 'DRINK-010'),
('Teh Gelas BIG 250ml', 3, '1 karton (24pcs)', 34000, 24, 2, 'DRINK-011'),
('Teh Gelas PET 200ml', 3, '1 karton (24pcs)', 49008, 24, 2, 'DRINK-012'),
('Teh Gelas Cup 180ml', 3, '1 karton (24pcs)', 19896, 24, 2, 'DRINK-013'),
('Susu Tango 200ml', 3, '1 karton (24pcs)', 90000, 24, 2, 'DRINK-014'),
('Vita Jelly Drink 150ml', 3, '1 karton (24pcs)', 19512, 24, 2, 'DRINK-015'),
('Isocup 175ml', 3, '1 karton (24pcs)', 19500, 24, 2, 'DRINK-016'),
('Torpedo XL 240ml', 3, '1 karton (24pcs)', 34978, 24, 2, 'DRINK-017'),
('You C Vitamin 140ml', 3, '1 karton (30btl)', 164850, 30, 2, 'DRINK-018'),
('Susu Tango Kido 115ml', 3, '1 karton (36pcs)', 89640, 36, 2, 'DRINK-019'),
('Crystalin Cup 200ml', 3, '1 karton (48pcs)', 25368, 48, 2, 'DRINK-020'),
('Kratingdaeng 150ml', 3, '1 karton (50btl)', 255300, 50, 2, 'DRINK-021');

-- Insert remaining Personal Care products (continuing from previous insert)
INSERT INTO products (name, category_id, packaging_description, price_per_package, units_per_package, packaging_type_id, sku) VALUES
-- Personal Care products
('PG FORMULA PRO 75GR', 4, '1 karton (12hgr)', 324000, 12, 4, 'CARE-001'),
('PRIMA PROTECT BODYWASH 400ML', 4, '1 karton (12pcs)', 374400, 12, 2, 'CARE-002'),
('PRIMA PROTECT BODYWASH 500ML', 4, '1 karton (12pcs)', 226800, 12, 2, 'CARE-003'),
('PG SPC JUN 45GR+SG', 4, '1 karton (24pcs)', 180000, 24, 2, 'CARE-004'),
('Mobile Oral Care', 4, '1 karton (2lsn)', 288000, 2, 9, 'CARE-005'),
('FORMULA PG JUN 45GR', 4, '1 karton (36pcs)', 180000, 36, 2, 'CARE-006'),
('PG FORMULA PROTEKSI 225GR', 4, '1 karton (3lsn)', 405000, 3, 9, 'CARE-007'),
('PG FORMULA STRONG 160GR (TWIN PACK)', 4, '1 karton (3lsn)', 612000, 3, 9, 'CARE-008'),
('PG FORMULA STRONG 160GR +SG FLEXY', 4, '1 karton (48pcs)', 480400, 48, 2, 'CARE-009'),
('PG FORMULA FAMILY 160GR +SG FLEXY', 4, '1 karton (48pcs)', 480000, 48, 2, 'CARE-010'),
('PG FORMULA SP WHITE 160GR', 4, '1 karton (48pcs)', 888000, 48, 2, 'CARE-011'),
('PG FORMULA CONFIDENT 160GR', 4, '1 karton (48pcs)', 888000, 48, 2, 'CARE-012'),
('PG FORMULA CHARCOAL 160GR', 4, '1 karton (48pcs)', 888000, 48, 2, 'CARE-013'),
('PG FORMULA PROTEKSI 75GR', 4, '1 karton (6lsn)', 220000, 6, 9, 'CARE-014'),
('SIKAT GIGI EKONOMI SINGLE', 4, '1 karton (6lsn)', 194400, 6, 9, 'CARE-015'),
('SIKAT GIGI EKONOMI PACK', 4, '1 karton (6lsn)', 558000, 6, 9, 'CARE-016'),
('Extreme Clean', 4, '1 karton (6lsn)', 806400, 6, 9, 'CARE-017'),
('Sensitive Active Care', 4, '1 karton (6lsn)', 792000, 6, 9, 'CARE-018'),
('Nano Charcoal Ultima', 4, '1 karton (6lsn)', 900000, 6, 9, 'CARE-019'),
('Sparkling White', 4, '1 karton (6lsn)', 878400, 6, 9, 'CARE-020'),
('Comfort Clean', 4, '1 karton (6lsn)', 734400, 6, 9, 'CARE-021'),
('Sensitive Flex', 4, '1 karton (6lsn)', 1584000, 6, 9, 'CARE-022'),
('SIKAT GIGI PREMIUM SINGLE', 4, '1 karton (72pcs)', 230400, 72, 2, 'CARE-023'),
('SIKAT GIGI PREMIUM PACK', 4, '1 karton (72pcs)', 633600, 72, 2, 'CARE-024');

-- Initialize inventory for all products with default stock levels
INSERT INTO inventory (product_id, current_stock, minimum_stock, maximum_stock)
SELECT 
    id as product_id,
    50 as current_stock,  -- Default starting stock
    10 as minimum_stock,  -- Minimum stock alert level
    200 as maximum_stock  -- Maximum stock capacity
FROM products;

-- Create default admin user (password should be hashed in real application)
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2b$10$example_hash_here', 'System Administrator', 'admin'),
('cashier1', '<EMAIL>', '$2b$10$example_hash_here', 'Cashier One', 'cashier');

-- Create sample transaction for testing
INSERT INTO sales_transactions (transaction_number, user_id, total_amount, tax_amount, discount_amount, final_amount, payment_method, payment_received, change_amount)
VALUES ('TXN-001', 2, 50000, 5000, 0, 55000, 'cash', 60000, 5000);

-- Add sample transaction items
INSERT INTO sales_transaction_items (transaction_id, product_id, quantity, unit_price, total_price)
VALUES 
(1, 1, 2, 5600, 11200),  -- 2 units of Mintz 115gr
(1, 10, 1, 4575, 4575);  -- 1 unit of Tango wafer Cheese
