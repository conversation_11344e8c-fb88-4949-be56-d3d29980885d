'use client';

import { useState, useEffect } from 'react';
import { Product, Category } from '../../types';
import { formatCurrency } from '../../utils/format';

interface ProductGridProps {
  onAddToCart: (product: Product, quantity: number) => void;
  searchTerm: string;
  selectedCategory: number | null;
}

export default function ProductGrid({ onAddToCart, searchTerm, selectedCategory }: ProductGridProps) {
  const [products, setProducts] = useState<(Product & { current_stock: number })[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [searchTerm, selectedCategory]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        limit: '50',
        is_active: 'true'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory) params.append('category_id', selectedCategory.toString());

      const response = await fetch(`/api/products?${params}`);
      const data = await response.json();

      if (data.success) {
        setProducts(data.data.data);
      } else {
        setError(data.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError('Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();

      if (data.success) {
        setCategories(data.data.data);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  const handleAddToCart = (product: Product & { current_stock: number }) => {
    if (product.current_stock <= 0) {
      alert('Product is out of stock');
      return;
    }
    onAddToCart(product, 1);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-600 text-center">
          <p className="text-lg font-semibold">Error</p>
          <p>{error}</p>
          <button
            onClick={fetchProducts}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {products.map((product) => (
        <div
          key={product.id}
          className={`bg-white rounded-lg shadow-md p-4 cursor-pointer transition-all hover:shadow-lg ${
            product.current_stock <= 0 ? 'opacity-50' : 'hover:scale-105'
          }`}
          onClick={() => handleAddToCart(product)}
        >
          <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
            <div className="text-gray-400 text-4xl">📦</div>
          </div>
          
          <h3 className="font-semibold text-sm mb-1 line-clamp-2" title={product.name}>
            {product.name}
          </h3>
          
          <p className="text-xs text-gray-600 mb-2">{product.packaging_description}</p>
          
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-lg font-bold text-blue-600">
                {formatCurrency(product.unit_price)}
              </span>
              <span className="text-xs text-gray-500">per unit</span>
            </div>
            
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-600">Package: {formatCurrency(product.price_per_package)}</span>
              <span className="text-gray-600">{product.units_per_package} units</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className={`text-xs ${product.current_stock <= 10 ? 'text-red-600' : 'text-green-600'}`}>
                Stock: {product.current_stock}
              </span>
              {product.sku && (
                <span className="text-xs text-gray-500">{product.sku}</span>
              )}
            </div>
          </div>
          
          {product.current_stock <= 0 && (
            <div className="mt-2 text-center">
              <span className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                Out of Stock
              </span>
            </div>
          )}
          
          {product.current_stock > 0 && product.current_stock <= 10 && (
            <div className="mt-2 text-center">
              <span className="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
                Low Stock
              </span>
            </div>
          )}
        </div>
      ))}
      
      {products.length === 0 && (
        <div className="col-span-full text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No products found</h3>
          <p className="text-gray-500">
            {searchTerm || selectedCategory
              ? 'Try adjusting your search or filter criteria'
              : 'No products available'}
          </p>
        </div>
      )}
    </div>
  );
}
