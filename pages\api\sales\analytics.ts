import { NextApiRequest, NextApiResponse } from 'next';
import { SalesTransactionModel } from '../../../models/SalesTransaction';
import { ApiResponse } from '../../../types';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `Method ${req.method} Not Allowed`
    });
  }

  try {
    const { start_date, end_date } = req.query;

    const analytics = await SalesTransactionModel.getSalesAnalytics(
      start_date as string,
      end_date as string
    );

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching sales analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch sales analytics'
    });
  }
}
