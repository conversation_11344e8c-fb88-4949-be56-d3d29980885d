{"name": "pos-arta", "version": "1.0.0", "description": "Point of Sale Application for Arta Store", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "node scripts/migrate-json-to-db.js", "db:setup": "psql -U postgres -f database/schema.sql", "db:seed": "psql -U postgres -d pos_arta -f database/migrate_data.sql", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:migration": "node scripts/test-migration.js", "test:api": "jest __tests__/api --verbose", "test:components": "jest __tests__/components --verbose", "test:integration": "jest __tests__/integration --verbose", "test:unit": "jest __tests__ --testPathIgnorePatterns=__tests__/integration --verbose"}, "keywords": ["pos", "point-of-sale", "retail", "inventory", "sales", "nextjs", "react"], "dependencies": {"@heroicons/react": "^2.0.18", "@hookform/resolvers": "5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "bcryptjs": "3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "4.1.0", "framer-motion": "^12.23.3", "jsonwebtoken": "^9.0.2", "lucide-react": "0.525.0", "motion-utils": "^12.23.2", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "3.1.0", "tailwind-merge": "3.3.1", "uuid": "11.1.0", "zod": "4.0.5", "zustand": "5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "16.3.0", "@types/bcryptjs": "3.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "10.0.0", "eslint-config-next": "15.3.5", "jest": "30.0.4", "jest-environment-jsdom": "30.0.4", "prisma": "^6.11.1", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "trustedDependencies": ["@prisma/client", "@prisma/engines", "@tailwindcss/oxide", "prisma", "sharp", "unrs-resolver"]}