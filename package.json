{"name": "pos-arta", "version": "1.0.0", "description": "Point of Sale Application for Arta Store", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "node scripts/migrate-json-to-db.js", "db:setup": "psql -U postgres -f database/schema.sql", "db:seed": "psql -U postgres -d pos_arta -f database/migrate_data.sql", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["pos", "point-of-sale", "retail", "inventory", "sales", "nextjs", "react"], "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "uuid": "^9.0.1", "@heroicons/react": "^2.0.18", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "tailwind-merge": "^2.2.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "prisma": "^5.7.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "eslint-config-next": "^14.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}