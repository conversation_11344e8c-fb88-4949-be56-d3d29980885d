import { ImageResponse } from 'next/og'

export const runtime = 'edge'

export const alt = 'POS Arta - Modern Point of Sale System'
export const size = {
  width: 1200,
  height: 630,
}
export const contentType = 'image/png'

export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          fontFamily: 'system-ui',
        }}
      >
        <div
          style={{
            background: 'white',
            borderRadius: '20px',
            padding: '60px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          }}
        >
          <div
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '20px',
              textAlign: 'center',
            }}
          >
            POS Arta
          </div>
          <div
            style={{
              fontSize: '32px',
              color: '#6b7280',
              textAlign: 'center',
              maxWidth: '800px',
              lineHeight: '1.4',
            }}
          >
            Modern Point of Sale System
          </div>
          <div
            style={{
              fontSize: '24px',
              color: '#9ca3af',
              textAlign: 'center',
              marginTop: '20px',
            }}
          >
            Built with Next.js 15, Prisma & shadcn/ui
          </div>
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
