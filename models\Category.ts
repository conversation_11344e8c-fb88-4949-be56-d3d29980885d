import { db } from "../lib/database";
import {
  Category,
  CreateCategoryRequest,
  PaginatedResponse,
  PaginationParams,
  UpdateCategoryRequest,
} from "../types";

export class CategoryModel {
  // Get all categories with optional pagination
  static async getAll(
    params?: PaginationParams
  ): Promise<PaginatedResponse<Category>> {
    const page = params?.page || 1;
    const limit = params?.limit || 50;
    const offset = (page - 1) * limit;
    const search = params?.search || "";
    const sortBy = params?.sort_by || "name";
    const sortOrder = params?.sort_order || "asc";

    let whereClause = "";
    let queryParams: any[] = [];
    let paramIndex = 1;

    if (search) {
      whereClause = `WHERE name ILIKE $${paramIndex} OR description ILIKE $${paramIndex}`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM categories ${whereClause}`;
    const countResult = await db.query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT * FROM categories 
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(limit, offset);

    const dataResult = await db.query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: dataResult.rows,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: totalItems,
        items_per_page: limit,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    };
  }

  // Get category by ID
  static async getById(id: number): Promise<Category | null> {
    const query = "SELECT * FROM categories WHERE id = $1";
    const result = await db.query(query, [id]);
    return result.rows[0] || null;
  }

  // Get category by name
  static async getByName(name: string): Promise<Category | null> {
    const query = "SELECT * FROM categories WHERE name = $1";
    const result = await db.query(query, [name]);
    return result.rows[0] || null;
  }

  // Create new category
  static async create(data: CreateCategoryRequest): Promise<Category> {
    const query = `
      INSERT INTO categories (name, description)
      VALUES ($1, $2)
      RETURNING *
    `;
    const result = await db.query(query, [data.name, data.description]);
    return result.rows[0];
  }

  // Update category
  static async update(
    id: number,
    data: UpdateCategoryRequest
  ): Promise<Category | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (data.name !== undefined) {
      fields.push(`name = $${paramIndex}`);
      values.push(data.name);
      paramIndex++;
    }

    if (data.description !== undefined) {
      fields.push(`description = $${paramIndex}`);
      values.push(data.description);
      paramIndex++;
    }

    if (fields.length === 0) {
      throw new Error("No fields to update");
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const query = `
      UPDATE categories 
      SET ${fields.join(", ")}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await db.query(query, values);
    return result.rows[0] || null;
  }

  // Delete category (soft delete by checking if it has products)
  static async delete(id: number): Promise<boolean> {
    // Check if category has products
    const productCheck = await db.query(
      "SELECT COUNT(*) FROM products WHERE category_id = $1",
      [id]
    );

    if (parseInt(productCheck.rows[0].count) > 0) {
      throw new Error("Cannot delete category that has products");
    }

    const query = "DELETE FROM categories WHERE id = $1";
    const result = await db.query(query, [id]);
    return result.rowCount > 0;
  }

  // Get categories with product count
  static async getCategoriesWithProductCount(): Promise<
    (Category & { product_count: number })[]
  > {
    const query = `
      SELECT 
        c.*,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
      GROUP BY c.id, c.name, c.description, c.created_at, c.updated_at
      ORDER BY c.name
    `;
    const result = await db.query(query);
    return result.rows.map((row) => ({
      ...row,
      product_count: parseInt(row.product_count),
    }));
  }

  // Check if category name exists (for validation)
  static async nameExists(name: string, excludeId?: number): Promise<boolean> {
    let query = "SELECT id FROM categories WHERE name = $1";
    const params: any[] = [name];

    if (excludeId) {
      query += " AND id != $2";
      params.push(excludeId);
    }

    const result = await db.query(query, params);
    return result.rows.length > 0;
  }

  // Get category statistics
  static async getStatistics(): Promise<{
    total_categories: number;
    categories_with_products: number;
    most_popular_category: { name: string; product_count: number } | null;
  }> {
    const statsQuery = `
      SELECT 
        COUNT(DISTINCT c.id) as total_categories,
        COUNT(DISTINCT CASE WHEN p.id IS NOT NULL THEN c.id END) as categories_with_products
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
    `;

    const popularQuery = `
      SELECT 
        c.name,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
      GROUP BY c.id, c.name
      ORDER BY product_count DESC
      LIMIT 1
    `;

    const [statsResult, popularResult] = await Promise.all([
      db.query(statsQuery),
      db.query(popularQuery),
    ]);

    const stats = statsResult.rows[0];
    const popular = popularResult.rows[0];

    return {
      total_categories: parseInt(stats.total_categories),
      categories_with_products: parseInt(stats.categories_with_products),
      most_popular_category: popular
        ? {
            name: popular.name,
            product_count: parseInt(popular.product_count),
          }
        : null,
    };
  }
}
