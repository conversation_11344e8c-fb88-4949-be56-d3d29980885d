#!/usr/bin/env node

/**
 * Migration script to transfer data from product.json to PostgreSQL database
 * This script reads the JSON file and inserts data into the normalized database structure
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Database configuration
const dbConfig = {
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'pos_arta',
    password: process.env.DB_PASSWORD || 'password',
    port: process.env.DB_PORT || 5432,
};

const pool = new Pool(dbConfig);

// Read and parse the JSON file
function readProductData() {
    try {
        const jsonPath = path.join(__dirname, '..', 'product.json');
        const rawData = fs.readFileSync(jsonPath, 'utf8');
        return JSON.parse(rawData);
    } catch (error) {
        console.error('Error reading product.json:', error);
        process.exit(1);
    }
}

// Extract unique categories from product data
function extractCategories(products) {
    const categories = [...new Set(products.map(p => p.kategori))];
    return categories.map(name => ({
        name,
        description: getCategoryDescription(name)
    }));
}

// Extract unique packaging types from product data
function extractPackagingTypes(products) {
    const packagingTypes = [...new Set(products.map(p => p['jenis-kemasan']))];
    return packagingTypes.map(name => ({
        name,
        description: getPackagingDescription(name)
    }));
}

// Helper function to get category descriptions
function getCategoryDescription(category) {
    const descriptions = {
        'Candy': 'Sweet confectionery products and candies',
        'Makanan': 'Food products including snacks, wafers, and packaged foods',
        'Minuman': 'Beverages including water, tea, juice, and energy drinks',
        'Personal Care': 'Personal hygiene and care products including toothpaste and toothbrushes'
    };
    return descriptions[category] || `${category} products`;
}

// Helper function to get packaging descriptions
function getPackagingDescription(packaging) {
    const descriptions = {
        'sak': 'Sak/bag packaging',
        'pcs': 'Individual pieces',
        'box': 'Box packaging',
        'hgr': 'Hanging/display packaging',
        'ban': 'Band/strip packaging',
        'rcg': 'Renceng/bundle packaging',
        'can': 'Can packaging',
        'karton': 'Carton packaging',
        'lsn': 'Lusin/dozen packaging',
        'btl': 'Bottle packaging'
    };
    return descriptions[packaging] || `${packaging} packaging`;
}

// Generate SKU based on category and sequence
function generateSKU(category, index) {
    const prefixes = {
        'Candy': 'CANDY',
        'Makanan': 'FOOD',
        'Minuman': 'DRINK',
        'Personal Care': 'CARE'
    };
    const prefix = prefixes[category] || 'PROD';
    return `${prefix}-${String(index).padStart(3, '0')}`;
}

// Insert categories into database
async function insertCategories(client, categories) {
    console.log('Inserting categories...');
    
    for (const category of categories) {
        try {
            await client.query(
                'INSERT INTO categories (name, description) VALUES ($1, $2) ON CONFLICT (name) DO NOTHING',
                [category.name, category.description]
            );
            console.log(`✓ Category: ${category.name}`);
        } catch (error) {
            console.error(`Error inserting category ${category.name}:`, error);
        }
    }
}

// Insert packaging types into database
async function insertPackagingTypes(client, packagingTypes) {
    console.log('Inserting packaging types...');
    
    for (const packagingType of packagingTypes) {
        try {
            await client.query(
                'INSERT INTO packaging_types (name, description) VALUES ($1, $2) ON CONFLICT (name) DO NOTHING',
                [packagingType.name, packagingType.description]
            );
            console.log(`✓ Packaging type: ${packagingType.name}`);
        } catch (error) {
            console.error(`Error inserting packaging type ${packagingType.name}:`, error);
        }
    }
}

// Get category ID by name
async function getCategoryId(client, categoryName) {
    const result = await client.query('SELECT id FROM categories WHERE name = $1', [categoryName]);
    return result.rows[0]?.id;
}

// Get packaging type ID by name
async function getPackagingTypeId(client, packagingTypeName) {
    const result = await client.query('SELECT id FROM packaging_types WHERE name = $1', [packagingTypeName]);
    return result.rows[0]?.id;
}

// Insert products into database
async function insertProducts(client, products) {
    console.log('Inserting products...');
    
    // Group products by category for SKU generation
    const productsByCategory = {};
    products.forEach(product => {
        if (!productsByCategory[product.kategori]) {
            productsByCategory[product.kategori] = [];
        }
        productsByCategory[product.kategori].push(product);
    });
    
    for (const [category, categoryProducts] of Object.entries(productsByCategory)) {
        for (let i = 0; i < categoryProducts.length; i++) {
            const product = categoryProducts[i];
            
            try {
                const categoryId = await getCategoryId(client, product.kategori);
                const packagingTypeId = await getPackagingTypeId(client, product['jenis-kemasan']);
                const sku = generateSKU(product.kategori, i + 1);
                
                await client.query(`
                    INSERT INTO products (
                        name, category_id, packaging_description, price_per_package, 
                        units_per_package, packaging_type_id, sku
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (sku) DO NOTHING
                `, [
                    product.produk,
                    categoryId,
                    product.kemasan,
                    product.harga,
                    product['jumlah-per-kemasan'],
                    packagingTypeId,
                    sku
                ]);
                
                console.log(`✓ Product: ${product.produk} (${sku})`);
            } catch (error) {
                console.error(`Error inserting product ${product.produk}:`, error);
            }
        }
    }
}

// Initialize inventory for all products
async function initializeInventory(client) {
    console.log('Initializing inventory...');
    
    try {
        await client.query(`
            INSERT INTO inventory (product_id, current_stock, minimum_stock, maximum_stock)
            SELECT 
                id as product_id,
                50 as current_stock,
                10 as minimum_stock,
                200 as maximum_stock
            FROM products
            WHERE id NOT IN (SELECT product_id FROM inventory)
        `);
        console.log('✓ Inventory initialized for all products');
    } catch (error) {
        console.error('Error initializing inventory:', error);
    }
}

// Create default users
async function createDefaultUsers(client) {
    console.log('Creating default users...');
    
    const users = [
        {
            username: 'admin',
            email: '<EMAIL>',
            password_hash: '$2b$10$example_hash_here', // In real app, use proper bcrypt hash
            full_name: 'System Administrator',
            role: 'admin'
        },
        {
            username: 'cashier1',
            email: '<EMAIL>',
            password_hash: '$2b$10$example_hash_here',
            full_name: 'Cashier One',
            role: 'cashier'
        }
    ];
    
    for (const user of users) {
        try {
            await client.query(`
                INSERT INTO users (username, email, password_hash, full_name, role)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (username) DO NOTHING
            `, [user.username, user.email, user.password_hash, user.full_name, user.role]);
            
            console.log(`✓ User: ${user.username} (${user.role})`);
        } catch (error) {
            console.error(`Error creating user ${user.username}:`, error);
        }
    }
}

// Main migration function
async function migrate() {
    const client = await pool.connect();
    
    try {
        console.log('Starting data migration...');
        console.log('Database:', dbConfig.database);
        console.log('Host:', dbConfig.host);
        console.log('---');
        
        // Begin transaction
        await client.query('BEGIN');
        
        // Read product data
        const products = readProductData();
        console.log(`Found ${products.length} products in JSON file`);
        
        // Extract and insert categories
        const categories = extractCategories(products);
        await insertCategories(client, categories);
        
        // Extract and insert packaging types
        const packagingTypes = extractPackagingTypes(products);
        await insertPackagingTypes(client, packagingTypes);
        
        // Insert products
        await insertProducts(client, products);
        
        // Initialize inventory
        await initializeInventory(client);
        
        // Create default users
        await createDefaultUsers(client);
        
        // Commit transaction
        await client.query('COMMIT');
        
        console.log('---');
        console.log('✅ Migration completed successfully!');
        
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        client.release();
        await pool.end();
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    migrate().catch(console.error);
}

module.exports = { migrate };
