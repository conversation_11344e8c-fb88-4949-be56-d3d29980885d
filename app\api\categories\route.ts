import { NextRequest, NextResponse } from 'next/server'
import { CategoryService } from '@/lib/models/category'
import { createCategorySchema, paginationSchema } from '@/lib/validations'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '50'),
      search: searchParams.get('search') || '',
      sortBy: searchParams.get('sort_by') || 'name',
      sortOrder: (searchParams.get('sort_order') || 'asc') as 'asc' | 'desc'
    }

    // Validate pagination parameters
    const validatedParams = paginationSchema.parse(params)
    
    const result = await CategoryService.getAll(validatedParams)
    
    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching categories:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid parameters',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch categories'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = createCategorySchema.parse(body)
    
    // Check if category name already exists
    const existingCategory = await CategoryService.getByName(validatedData.name)
    if (existingCategory) {
      return NextResponse.json({
        success: false,
        error: 'Category name already exists'
      }, { status: 409 })
    }
    
    const category = await CategoryService.create(validatedData)
    
    return NextResponse.json({
      success: true,
      data: category,
      message: 'Category created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating category:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create category'
    }, { status: 500 })
  }
}
