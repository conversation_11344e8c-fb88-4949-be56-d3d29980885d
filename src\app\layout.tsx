import { Toaster } from "@/components/ui/toaster";
import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "POS Arta - Point of Sale System",
    template: "%s | POS Arta",
  },
  description:
    "Modern Point of Sale application built with Next.js, Prisma, and shadcn/ui. Streamline your retail operations with our comprehensive POS solution.",
  keywords: [
    "POS",
    "Point of Sale",
    "Retail",
    "Inventory Management",
    "Sales Tracking",
    "Next.js",
    "Prisma",
    "shadcn/ui",
  ],
  authors: [{ name: "POS Arta Team" }],
  creator: "POS Arta",
  publisher: "POS Arta",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://pos-arta.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://pos-arta.com",
    title: "POS Arta - Modern Point of Sale System",
    description:
      "Streamline your retail operations with our comprehensive POS solution built with modern web technologies.",
    siteName: "POS Arta",
  },
  twitter: {
    card: "summary_large_image",
    title: "POS Arta - Modern Point of Sale System",
    description:
      "Streamline your retail operations with our comprehensive POS solution.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0a0a0a" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
        <Toaster />
      </body>
    </html>
  );
}
