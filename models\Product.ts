import { db } from '../lib/database';
import {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  ProductFilters,
  PaginatedResponse,
  Inventory
} from '../types';

export class ProductModel {
  // Get all products with filters and pagination
  static async getAll(filters?: ProductFilters): Promise<PaginatedResponse<Product & { current_stock: number }>> {
    const page = filters?.page || 1;
    const limit = filters?.limit || 20;
    const offset = (page - 1) * limit;
    const search = filters?.search || '';
    const sortBy = filters?.sort_by || 'name';
    const sortOrder = filters?.sort_order || 'asc';

    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Search filter
    if (search) {
      whereConditions.push(`(p.name ILIKE $${paramIndex} OR p.sku ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Category filter
    if (filters?.category_id) {
      whereConditions.push(`p.category_id = $${paramIndex}`);
      queryParams.push(filters.category_id);
      paramIndex++;
    }

    // Active status filter
    if (filters?.is_active !== undefined) {
      whereConditions.push(`p.is_active = $${paramIndex}`);
      queryParams.push(filters.is_active);
      paramIndex++;
    }

    // Price range filters
    if (filters?.min_price) {
      whereConditions.push(`p.price_per_package >= $${paramIndex}`);
      queryParams.push(filters.min_price);
      paramIndex++;
    }

    if (filters?.max_price) {
      whereConditions.push(`p.price_per_package <= $${paramIndex}`);
      queryParams.push(filters.max_price);
      paramIndex++;
    }

    // Low stock filter
    if (filters?.low_stock) {
      whereConditions.push(`i.current_stock <= i.minimum_stock`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Base query with joins
    const baseQuery = `
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN packaging_types pt ON p.packaging_type_id = pt.id
      LEFT JOIN inventory i ON p.id = i.product_id
      ${whereClause}
    `;

    // Get total count
    const countQuery = `SELECT COUNT(*) ${baseQuery}`;
    const countResult = await db.query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT 
        p.*,
        c.name as category_name,
        pt.name as packaging_type_name,
        COALESCE(i.current_stock, 0) as current_stock
      ${baseQuery}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(limit, offset);

    const dataResult = await db.query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: dataResult.rows,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: totalItems,
        items_per_page: limit,
        has_next: page < totalPages,
        has_prev: page > 1
      }
    };
  }

  // Get product by ID with related data
  static async getById(id: number): Promise<(Product & { current_stock: number; category_name: string; packaging_type_name: string }) | null> {
    const query = `
      SELECT 
        p.*,
        c.name as category_name,
        pt.name as packaging_type_name,
        COALESCE(i.current_stock, 0) as current_stock
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN packaging_types pt ON p.packaging_type_id = pt.id
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE p.id = $1
    `;
    const result = await db.query(query, [id]);
    return result.rows[0] || null;
  }

  // Get product by SKU
  static async getBySku(sku: string): Promise<Product | null> {
    const query = 'SELECT * FROM products WHERE sku = $1';
    const result = await db.query(query, [sku]);
    return result.rows[0] || null;
  }

  // Get product by barcode
  static async getByBarcode(barcode: string): Promise<Product | null> {
    const query = 'SELECT * FROM products WHERE barcode = $1';
    const result = await db.query(query, [barcode]);
    return result.rows[0] || null;
  }

  // Create new product
  static async create(data: CreateProductRequest): Promise<Product> {
    const query = `
      INSERT INTO products (
        name, category_id, packaging_description, price_per_package,
        units_per_package, packaging_type_id, sku, barcode, description
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;
    
    const result = await db.query(query, [
      data.name,
      data.category_id,
      data.packaging_description,
      data.price_per_package,
      data.units_per_package,
      data.packaging_type_id,
      data.sku,
      data.barcode,
      data.description
    ]);

    const product = result.rows[0];

    // Initialize inventory for the new product
    await db.query(
      'INSERT INTO inventory (product_id, current_stock, minimum_stock, maximum_stock) VALUES ($1, $2, $3, $4)',
      [product.id, 0, 10, 200]
    );

    return product;
  }

  // Update product
  static async update(id: number, data: UpdateProductRequest): Promise<Product | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    const updateableFields = [
      'name', 'category_id', 'packaging_description', 'price_per_package',
      'units_per_package', 'packaging_type_id', 'sku', 'barcode', 'description', 'is_active'
    ];

    updateableFields.forEach(field => {
      if (data[field as keyof UpdateProductRequest] !== undefined) {
        fields.push(`${field} = $${paramIndex}`);
        values.push(data[field as keyof UpdateProductRequest]);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const query = `
      UPDATE products 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await db.query(query, values);
    return result.rows[0] || null;
  }

  // Delete product (soft delete by setting is_active to false)
  static async delete(id: number): Promise<boolean> {
    const query = 'UPDATE products SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1';
    const result = await db.query(query, [id]);
    return result.rowCount > 0;
  }

  // Hard delete product (use with caution)
  static async hardDelete(id: number): Promise<boolean> {
    // Check if product has been sold
    const salesCheck = await db.query(
      'SELECT COUNT(*) FROM sales_transaction_items WHERE product_id = $1',
      [id]
    );

    if (parseInt(salesCheck.rows[0].count) > 0) {
      throw new Error('Cannot delete product that has sales history');
    }

    const query = 'DELETE FROM products WHERE id = $1';
    const result = await db.query(query, [id]);
    return result.rowCount > 0;
  }

  // Get products with low stock
  static async getLowStockProducts(): Promise<(Product & { current_stock: number; minimum_stock: number })[]> {
    const query = `
      SELECT 
        p.*,
        i.current_stock,
        i.minimum_stock
      FROM products p
      JOIN inventory i ON p.id = i.product_id
      WHERE p.is_active = true AND i.current_stock <= i.minimum_stock
      ORDER BY (i.current_stock::float / i.minimum_stock::float) ASC
    `;
    const result = await db.query(query);
    return result.rows;
  }

  // Get top selling products
  static async getTopSellingProducts(limit: number = 10): Promise<(Product & { total_sold: number; revenue: number })[]> {
    const query = `
      SELECT 
        p.*,
        SUM(sti.quantity) as total_sold,
        SUM(sti.total_price) as revenue
      FROM products p
      JOIN sales_transaction_items sti ON p.id = sti.product_id
      JOIN sales_transactions st ON sti.transaction_id = st.id
      WHERE st.status = 'completed' AND p.is_active = true
      GROUP BY p.id
      ORDER BY total_sold DESC
      LIMIT $1
    `;
    const result = await db.query(query, [limit]);
    return result.rows.map(row => ({
      ...row,
      total_sold: parseInt(row.total_sold),
      revenue: parseFloat(row.revenue)
    }));
  }

  // Check if SKU exists (for validation)
  static async skuExists(sku: string, excludeId?: number): Promise<boolean> {
    let query = 'SELECT id FROM products WHERE sku = $1';
    const params: any[] = [sku];

    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }

    const result = await db.query(query, params);
    return result.rows.length > 0;
  }

  // Check if barcode exists (for validation)
  static async barcodeExists(barcode: string, excludeId?: number): Promise<boolean> {
    let query = 'SELECT id FROM products WHERE barcode = $1';
    const params: any[] = [barcode];

    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }

    const result = await db.query(query, params);
    return result.rows.length > 0;
  }

  // Get product statistics
  static async getStatistics(): Promise<{
    total_products: number;
    active_products: number;
    low_stock_products: number;
    out_of_stock_products: number;
    total_inventory_value: number;
  }> {
    const query = `
      SELECT 
        COUNT(*) as total_products,
        COUNT(CASE WHEN p.is_active = true THEN 1 END) as active_products,
        COUNT(CASE WHEN p.is_active = true AND i.current_stock <= i.minimum_stock AND i.current_stock > 0 THEN 1 END) as low_stock_products,
        COUNT(CASE WHEN p.is_active = true AND i.current_stock = 0 THEN 1 END) as out_of_stock_products,
        COALESCE(SUM(CASE WHEN p.is_active = true THEN p.price_per_package * i.current_stock END), 0) as total_inventory_value
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
    `;
    
    const result = await db.query(query);
    const stats = result.rows[0];

    return {
      total_products: parseInt(stats.total_products),
      active_products: parseInt(stats.active_products),
      low_stock_products: parseInt(stats.low_stock_products),
      out_of_stock_products: parseInt(stats.out_of_stock_products),
      total_inventory_value: parseFloat(stats.total_inventory_value)
    };
  }
}
