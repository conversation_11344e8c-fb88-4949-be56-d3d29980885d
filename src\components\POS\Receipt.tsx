'use client';

import { useRef } from 'react';
import { SalesTransaction, SalesTransactionItem } from '../../types';
import { formatCurrency, formatDate, formatTime } from '../../utils/format';
import { PrinterIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface ReceiptProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: SalesTransaction & { items: SalesTransactionItem[] };
}

export default function Receipt({ isOpen, onClose, transaction }: ReceiptProps) {
  const receiptRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (receiptRef.current) {
      const printContent = receiptRef.current.innerHTML;
      const originalContent = document.body.innerHTML;
      
      document.body.innerHTML = printContent;
      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload(); // Reload to restore React functionality
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">Receipt</h2>
          <div className="flex space-x-2">
            <button
              onClick={handlePrint}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
              title="Print Receipt"
            >
              <PrinterIcon className="h-5 w-5" />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Receipt Content */}
        <div ref={receiptRef} className="p-6 font-mono text-sm">
          {/* Store Header */}
          <div className="text-center mb-6">
            <h1 className="text-xl font-bold">ARTA STORE</h1>
            <p className="text-gray-600">Point of Sale System</p>
            <p className="text-gray-600">Jl. Contoh No. 123, Jakarta</p>
            <p className="text-gray-600">Tel: (021) 1234-5678</p>
          </div>

          {/* Transaction Info */}
          <div className="border-t border-b border-gray-300 py-3 mb-4">
            <div className="flex justify-between">
              <span>Transaction #:</span>
              <span className="font-semibold">{transaction.transaction_number}</span>
            </div>
            <div className="flex justify-between">
              <span>Date:</span>
              <span>{formatDate(transaction.created_at, 'datetime')}</span>
            </div>
            <div className="flex justify-between">
              <span>Cashier:</span>
              <span>{transaction.user_name || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span>Payment:</span>
              <span className="uppercase">{transaction.payment_method}</span>
            </div>
          </div>

          {/* Items */}
          <div className="mb-4">
            <div className="border-b border-gray-300 pb-2 mb-2">
              <div className="flex justify-between font-semibold">
                <span>ITEM</span>
                <span>TOTAL</span>
              </div>
            </div>
            
            {transaction.items.map((item, index) => (
              <div key={index} className="mb-3">
                <div className="flex justify-between">
                  <span className="flex-1 truncate pr-2">
                    {item.product_name}
                  </span>
                  <span className="font-semibold">
                    {formatCurrency(item.total_price)}
                  </span>
                </div>
                <div className="text-gray-600 text-xs flex justify-between">
                  <span>
                    {item.quantity} x {formatCurrency(item.unit_price)}
                  </span>
                  {item.product_sku && (
                    <span>SKU: {item.product_sku}</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Totals */}
          <div className="border-t border-gray-300 pt-3 space-y-1">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>{formatCurrency(transaction.total_amount)}</span>
            </div>
            
            {transaction.discount_amount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Discount:</span>
                <span>-{formatCurrency(transaction.discount_amount)}</span>
              </div>
            )}
            
            <div className="flex justify-between">
              <span>Tax (10%):</span>
              <span>{formatCurrency(transaction.tax_amount)}</span>
            </div>
            
            <div className="border-t border-gray-300 pt-1">
              <div className="flex justify-between font-bold text-lg">
                <span>TOTAL:</span>
                <span>{formatCurrency(transaction.final_amount)}</span>
              </div>
            </div>
            
            <div className="flex justify-between">
              <span>Payment Received:</span>
              <span>{formatCurrency(transaction.payment_received)}</span>
            </div>
            
            <div className="flex justify-between font-semibold">
              <span>Change:</span>
              <span>{formatCurrency(transaction.change_amount)}</span>
            </div>
          </div>

          {/* Notes */}
          {transaction.notes && (
            <div className="border-t border-gray-300 pt-3 mt-3">
              <div className="text-gray-600">
                <span className="font-semibold">Notes:</span>
                <p className="mt-1">{transaction.notes}</p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t border-gray-300 pt-4 mt-6 text-center text-xs text-gray-600">
            <p>Thank you for shopping with us!</p>
            <p>Please keep this receipt for your records</p>
            <p className="mt-2">
              Printed on {formatDate(new Date(), 'datetime')}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="border-t border-gray-200 p-4 flex space-x-3">
          <button
            onClick={handlePrint}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2"
          >
            <PrinterIcon className="h-4 w-4" />
            <span>Print Receipt</span>
          </button>
          <button
            onClick={onClose}
            className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
