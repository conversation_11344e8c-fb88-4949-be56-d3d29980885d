"use client";

import { Bars3Icon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import Cart from "../components/POS/Cart";
import CheckoutModal from "../components/POS/CheckoutModal";
import ProductGrid from "../components/POS/ProductGrid";
import Receipt from "../components/POS/Receipt";
import {
  CartItem,
  Category,
  CreateSalesTransactionRequest,
  Product,
  SalesTransaction,
  SalesTransactionItem,
} from "../types";

export default function POSPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showCheckout, setShowCheckout] = useState(false);
  const [showReceipt, setShowReceipt] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState<
    (SalesTransaction & { items: SalesTransactionItem[] }) | null
  >(null);

  // Tax rate (10%)
  const TAX_RATE = 0.1;

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/categories");
      const data = await response.json();
      if (data.success) {
        setCategories(data.data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const addToCart = (product: Product, quantity: number) => {
    setCartItems((prevItems) => {
      const existingItem = prevItems.find(
        (item) => item.product.id === product.id
      );

      if (existingItem) {
        return prevItems.map((item) =>
          item.product.id === product.id
            ? {
                ...item,
                quantity: item.quantity + quantity,
                total_price: (item.quantity + quantity) * item.unit_price,
              }
            : item
        );
      } else {
        return [
          ...prevItems,
          {
            product,
            quantity,
            unit_price: product.unit_price,
            total_price: quantity * product.unit_price,
          },
        ];
      }
    });
  };

  const updateQuantity = (productId: number, quantity: number) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.product.id === productId
          ? {
              ...item,
              quantity,
              total_price: quantity * item.unit_price,
            }
          : item
      )
    );
  };

  const removeItem = (productId: number) => {
    setCartItems((prevItems) =>
      prevItems.filter((item) => item.product.id !== productId)
    );
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const handleCheckout = () => {
    setShowCheckout(true);
  };

  const handleConfirmSale = async (
    transactionData: CreateSalesTransactionRequest
  ) => {
    try {
      const response = await fetch("/api/sales", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(transactionData),
      });

      const result = await response.json();

      if (result.success) {
        // Clear cart
        setCartItems([]);
        setShowCheckout(false);

        // Set transaction for receipt
        setCurrentTransaction(result.data);
        setShowReceipt(true);
      } else {
        throw new Error(result.error || "Failed to process transaction");
      }
    } catch (error) {
      console.error("Transaction error:", error);
      throw error;
    }
  };

  // Calculate totals
  const subtotal = cartItems.reduce((sum, item) => sum + item.total_price, 0);
  const discountAmount = 0; // TODO: Implement discount logic
  const taxAmount = (subtotal - discountAmount) * TAX_RATE;
  const total = subtotal - discountAmount + taxAmount;

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSidebar(!showSidebar)}
                className="p-2 rounded-md hover:bg-gray-100"
              >
                <Bars3Icon className="h-6 w-6" />
              </button>
              <h1 className="text-xl font-bold text-gray-800">POS Arta</h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                />
              </div>

              <div className="text-sm text-gray-600">
                <span className="font-medium">Cashier:</span> Admin
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-73px)]">
        {/* Sidebar - Categories */}
        {showSidebar && (
          <div className="w-64 bg-white shadow-sm border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Categories
              </h2>

              <div className="space-y-2">
                <button
                  onClick={() => setSelectedCategory(null)}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    selectedCategory === null
                      ? "bg-blue-100 text-blue-800 font-medium"
                      : "hover:bg-gray-100 text-gray-700"
                  }`}
                >
                  All Products
                </button>

                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      selectedCategory === category.id
                        ? "bg-blue-100 text-blue-800 font-medium"
                        : "hover:bg-gray-100 text-gray-700"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Product Grid */}
          <div className="flex-1 p-6 overflow-y-auto">
            <ProductGrid
              onAddToCart={addToCart}
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
            />
          </div>

          {/* Cart */}
          <div className="w-96 p-6">
            <Cart
              items={cartItems}
              onUpdateQuantity={updateQuantity}
              onRemoveItem={removeItem}
              onClearCart={clearCart}
              onCheckout={handleCheckout}
              subtotal={subtotal}
              taxAmount={taxAmount}
              discountAmount={discountAmount}
              total={total}
            />
          </div>
        </div>
      </div>

      {/* Checkout Modal */}
      <CheckoutModal
        isOpen={showCheckout}
        onClose={() => setShowCheckout(false)}
        cartItems={cartItems}
        subtotal={subtotal}
        taxAmount={taxAmount}
        discountAmount={discountAmount}
        total={total}
        onConfirmSale={handleConfirmSale}
      />

      {/* Receipt Modal */}
      {currentTransaction && (
        <Receipt
          isOpen={showReceipt}
          onClose={() => setShowReceipt(false)}
          transaction={currentTransaction}
        />
      )}
    </div>
  );
}
