import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/products/route'
import { GET as GetById, PUT, DELETE } from '@/app/api/products/[id]/route'
import { prisma } from '@/lib/prisma'

// Mock the Prisma client
jest.mock('@/lib/prisma')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/products', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/products', () => {
    it('should return products with pagination', async () => {
      const mockProducts = [
        {
          id: 1,
          name: 'Chocolate Bar',
          categoryId: 1,
          packagingDescription: '50g bar',
          pricePerPackage: 2.50,
          unitsPerPackage: 1,
          packagingTypeId: 1,
          unitPrice: 2.50,
          sku: 'CHOC001',
          barcode: '1234567890123',
          description: 'Milk chocolate bar',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          category: { id: 1, name: '<PERSON>' },
          packagingType: { id: 1, name: 'Bar' },
          inventory: { currentStock: 100 }
        }
      ]

      mockPrisma.product.findMany.mockResolvedValue(mockProducts as any)
      mockPrisma.product.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/products')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].name).toBe('Chocolate Bar')
      expect(data.data.pagination.totalItems).toBe(1)
    })

    it('should filter products by category', async () => {
      const mockProducts = [
        {
          id: 1,
          name: 'Chocolate Bar',
          categoryId: 1,
          category: { id: 1, name: 'Candy' }
        }
      ]

      mockPrisma.product.findMany.mockResolvedValue(mockProducts as any)
      mockPrisma.product.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/products?category_id=1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            categoryId: 1
          })
        })
      )
    })

    it('should search products by name', async () => {
      const mockProducts = [
        {
          id: 1,
          name: 'Chocolate Bar',
          categoryId: 1
        }
      ]

      mockPrisma.product.findMany.mockResolvedValue(mockProducts as any)
      mockPrisma.product.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/products?search=chocolate')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                name: expect.objectContaining({
                  contains: 'chocolate'
                })
              })
            ])
          })
        })
      )
    })
  })

  describe('POST /api/products', () => {
    it('should create a new product', async () => {
      const newProduct = {
        name: 'New Chocolate',
        categoryId: 1,
        packagingDescription: '100g bar',
        pricePerPackage: 5.00,
        unitsPerPackage: 1,
        packagingTypeId: 1,
        sku: 'CHOC002',
        description: 'Dark chocolate bar'
      }

      const mockCreatedProduct = {
        id: 2,
        ...newProduct,
        unitPrice: 5.00,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockPrisma.product.findFirst.mockResolvedValue(null) // No duplicate
      mockPrisma.product.create.mockResolvedValue(mockCreatedProduct as any)

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(newProduct)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.name).toBe(newProduct.name)
      expect(data.message).toBe('Product created successfully')
    })

    it('should return error for duplicate product name', async () => {
      const duplicateProduct = {
        name: 'Existing Product',
        categoryId: 1,
        packagingDescription: '50g',
        pricePerPackage: 2.50,
        unitsPerPackage: 1,
        packagingTypeId: 1
      }

      mockPrisma.product.findFirst.mockResolvedValue({ id: 1, name: 'Existing Product' } as any)

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(duplicateProduct)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Product name already exists')
    })

    it('should return validation error for invalid data', async () => {
      const invalidProduct = {
        name: '', // Empty name should fail validation
        categoryId: 'invalid', // Should be number
        pricePerPackage: -1 // Should be positive
      }

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(invalidProduct)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation failed')
      expect(data.details).toBeDefined()
    })
  })

  describe('GET /api/products/[id]', () => {
    it('should return product by ID', async () => {
      const mockProduct = {
        id: 1,
        name: 'Chocolate Bar',
        categoryId: 1,
        category: { id: 1, name: 'Candy' },
        packagingType: { id: 1, name: 'Bar' },
        inventory: { currentStock: 100 }
      }

      mockPrisma.product.findUnique.mockResolvedValue(mockProduct as any)

      const response = await GetById(
        new NextRequest('http://localhost:3000/api/products/1'),
        { params: { id: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.id).toBe(1)
      expect(data.data.name).toBe('Chocolate Bar')
    })

    it('should return 404 for non-existent product', async () => {
      mockPrisma.product.findUnique.mockResolvedValue(null)

      const response = await GetById(
        new NextRequest('http://localhost:3000/api/products/999'),
        { params: { id: '999' } }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Product not found')
    })

    it('should return 400 for invalid ID', async () => {
      const response = await GetById(
        new NextRequest('http://localhost:3000/api/products/invalid'),
        { params: { id: 'invalid' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid product ID')
    })
  })

  describe('PUT /api/products/[id]', () => {
    it('should update product', async () => {
      const updateData = {
        name: 'Updated Chocolate',
        description: 'Updated description'
      }

      const existingProduct = {
        id: 1,
        name: 'Chocolate Bar',
        categoryId: 1
      }

      const updatedProduct = {
        ...existingProduct,
        ...updateData,
        updatedAt: new Date()
      }

      mockPrisma.product.findUnique.mockResolvedValue(existingProduct as any)
      mockPrisma.product.findFirst.mockResolvedValue(null) // No name conflict
      mockPrisma.product.update.mockResolvedValue(updatedProduct as any)

      const response = await PUT(
        new NextRequest('http://localhost:3000/api/products/1', {
          method: 'PUT',
          body: JSON.stringify(updateData)
        }),
        { params: { id: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.name).toBe(updateData.name)
      expect(data.message).toBe('Product updated successfully')
    })
  })

  describe('DELETE /api/products/[id]', () => {
    it('should delete product', async () => {
      const existingProduct = {
        id: 1,
        name: 'Chocolate Bar'
      }

      mockPrisma.product.findUnique.mockResolvedValue(existingProduct as any)
      mockPrisma.product.delete.mockResolvedValue(existingProduct as any)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/products/1'),
        { params: { id: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Product deleted successfully')
    })

    it('should return 404 for non-existent product', async () => {
      mockPrisma.product.findUnique.mockResolvedValue(null)

      const response = await DELETE(
        new NextRequest('http://localhost:3000/api/products/999'),
        { params: { id: '999' } }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Product not found')
    })
  })
})
