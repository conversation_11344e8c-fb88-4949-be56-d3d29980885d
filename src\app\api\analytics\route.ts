import { NextRequest, NextResponse } from 'next/server'
import { SalesTransactionService } from '@/lib/models/sales-transaction'
import { ProductService } from '@/lib/models/product'
import { CategoryService } from '@/lib/models/category'
import { InventoryService } from '@/lib/models/inventory'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    
    const periodDays = parseInt(period)
    if (isNaN(periodDays) || periodDays < 1 || periodDays > 365) {
      return NextResponse.json({
        success: false,
        error: 'Period must be between 1 and 365 days'
      }, { status: 400 })
    }
    
    // Calculate date range
    const end = endDate ? new Date(endDate) : new Date()
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - (periodDays * 24 * 60 * 60 * 1000))
    
    // Get analytics data
    const [
      salesSummary,
      topProducts,
      salesByCategory,
      dailySales,
      inventoryAlerts,
      totalProducts,
      totalCategories
    ] = await Promise.all([
      SalesTransactionService.getSalesSummary(start, end),
      SalesTransactionService.getTopProducts(start, end, 10),
      SalesTransactionService.getSalesByCategory(start, end),
      SalesTransactionService.getDailySales(start, end),
      InventoryService.getAlerts('all'),
      ProductService.getCount(),
      CategoryService.getCount()
    ])
    
    const analytics = {
      period: {
        start: start.toISOString(),
        end: end.toISOString(),
        days: periodDays
      },
      summary: {
        totalSales: salesSummary.totalSales || 0,
        totalTransactions: salesSummary.totalTransactions || 0,
        averageTransaction: salesSummary.averageTransaction || 0,
        totalProducts,
        totalCategories,
        lowStockItems: inventoryAlerts.filter(alert => alert.status === 'low_stock').length,
        outOfStockItems: inventoryAlerts.filter(alert => alert.status === 'out_of_stock').length
      },
      topProducts: topProducts || [],
      salesByCategory: salesByCategory || [],
      dailySales: dailySales || [],
      inventoryAlerts: inventoryAlerts || []
    }
    
    return NextResponse.json({
      success: true,
      data: analytics
    })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch analytics data'
    }, { status: 500 })
  }
}

// Get specific analytics report
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { reportType, startDate, endDate, filters } = body
    
    if (!reportType) {
      return NextResponse.json({
        success: false,
        error: 'Report type is required'
      }, { status: 400 })
    }
    
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const end = endDate ? new Date(endDate) : new Date()
    
    let reportData
    
    switch (reportType) {
      case 'sales_summary':
        reportData = await SalesTransactionService.getSalesSummary(start, end)
        break
        
      case 'top_products':
        const limit = filters?.limit || 20
        reportData = await SalesTransactionService.getTopProducts(start, end, limit)
        break
        
      case 'sales_by_category':
        reportData = await SalesTransactionService.getSalesByCategory(start, end)
        break
        
      case 'daily_sales':
        reportData = await SalesTransactionService.getDailySales(start, end)
        break
        
      case 'inventory_report':
        reportData = await InventoryService.getInventoryReport(filters)
        break
        
      case 'low_stock_report':
        reportData = await InventoryService.getAlerts('low_stock')
        break
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid report type'
        }, { status: 400 })
    }
    
    return NextResponse.json({
      success: true,
      data: {
        reportType,
        period: {
          start: start.toISOString(),
          end: end.toISOString()
        },
        data: reportData
      }
    })
  } catch (error) {
    console.error('Error generating report:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate report'
    }, { status: 500 })
  }
}
