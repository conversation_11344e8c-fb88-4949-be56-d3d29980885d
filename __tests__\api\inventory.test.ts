import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/inventory/route'
import { GET as GetByProductId, PUT, PATCH } from '@/app/api/inventory/[productId]/route'
import { prisma } from '@/lib/prisma'

// Mock the Prisma client
jest.mock('@/lib/prisma')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('/api/inventory', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/inventory', () => {
    it('should return inventory overview with pagination', async () => {
      const mockInventory = [
        {
          id: 1,
          productId: 1,
          currentStock: 100,
          minimumStock: 10,
          maximumStock: 500,
          lastRestockedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          product: {
            id: 1,
            name: 'Chocolate Bar',
            sku: 'CHOC001',
            category: { name: 'Candy' }
          }
        }
      ]

      mockPrisma.inventory.findMany.mockResolvedValue(mockInventory as any)
      mockPrisma.inventory.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/inventory')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.data).toHaveLength(1)
      expect(data.data.data[0].currentStock).toBe(100)
      expect(data.data.pagination.totalItems).toBe(1)
    })

    it('should filter low stock items', async () => {
      const mockLowStockInventory = [
        {
          id: 1,
          productId: 1,
          currentStock: 5,
          minimumStock: 10,
          product: { name: 'Low Stock Item' }
        }
      ]

      mockPrisma.inventory.findMany.mockResolvedValue(mockLowStockInventory as any)
      mockPrisma.inventory.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/inventory?low_stock=true')
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockPrisma.inventory.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            currentStock: expect.objectContaining({
              lte: expect.any(Object)
            })
          })
        })
      )
    })

    it('should filter out of stock items', async () => {
      const mockOutOfStockInventory = [
        {
          id: 1,
          productId: 1,
          currentStock: 0,
          minimumStock: 10,
          product: { name: 'Out of Stock Item' }
        }
      ]

      mockPrisma.inventory.findMany.mockResolvedValue(mockOutOfStockInventory as any)
      mockPrisma.inventory.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/inventory?out_of_stock=true')
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockPrisma.inventory.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            currentStock: 0
          })
        })
      )
    })
  })

  describe('POST /api/inventory', () => {
    it('should return inventory alerts', async () => {
      const mockAlerts = [
        {
          productId: 1,
          productName: 'Low Stock Item',
          currentStock: 5,
          minimumStock: 10,
          status: 'low_stock'
        },
        {
          productId: 2,
          productName: 'Out of Stock Item',
          currentStock: 0,
          minimumStock: 5,
          status: 'out_of_stock'
        }
      ]

      // Mock the InventoryService.getAlerts method
      jest.doMock('@/lib/models/inventory', () => ({
        InventoryService: {
          getAlerts: jest.fn().mockResolvedValue(mockAlerts)
        }
      }))

      const request = new NextRequest('http://localhost:3000/api/inventory', {
        method: 'POST',
        body: JSON.stringify({ type: 'all' })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(2)
    })

    it('should return error for invalid alert type', async () => {
      const request = new NextRequest('http://localhost:3000/api/inventory', {
        method: 'POST',
        body: JSON.stringify({ type: 'invalid_type' })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Invalid alert type')
    })
  })

  describe('GET /api/inventory/[productId]', () => {
    it('should return inventory for specific product', async () => {
      const mockInventory = {
        id: 1,
        productId: 1,
        currentStock: 100,
        minimumStock: 10,
        maximumStock: 500,
        lastRestockedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        product: {
          id: 1,
          name: 'Chocolate Bar',
          sku: 'CHOC001'
        }
      }

      mockPrisma.inventory.findUnique.mockResolvedValue(mockInventory as any)

      const response = await GetByProductId(
        new NextRequest('http://localhost:3000/api/inventory/1'),
        { params: { productId: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.productId).toBe(1)
      expect(data.data.currentStock).toBe(100)
    })

    it('should return 404 for non-existent inventory', async () => {
      mockPrisma.inventory.findUnique.mockResolvedValue(null)

      const response = await GetByProductId(
        new NextRequest('http://localhost:3000/api/inventory/999'),
        { params: { productId: '999' } }
      )
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Inventory record not found')
    })

    it('should return 400 for invalid product ID', async () => {
      const response = await GetByProductId(
        new NextRequest('http://localhost:3000/api/inventory/invalid'),
        { params: { productId: 'invalid' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid product ID')
    })
  })

  describe('PUT /api/inventory/[productId]', () => {
    it('should update inventory levels', async () => {
      const updateData = {
        currentStock: 150,
        minimumStock: 20,
        maximumStock: 600
      }

      const existingInventory = {
        id: 1,
        productId: 1,
        currentStock: 100,
        minimumStock: 10,
        maximumStock: 500
      }

      const updatedInventory = {
        ...existingInventory,
        ...updateData,
        updatedAt: new Date()
      }

      mockPrisma.inventory.findUnique.mockResolvedValue(existingInventory as any)
      mockPrisma.inventory.update.mockResolvedValue(updatedInventory as any)

      const response = await PUT(
        new NextRequest('http://localhost:3000/api/inventory/1', {
          method: 'PUT',
          body: JSON.stringify(updateData)
        }),
        { params: { productId: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.currentStock).toBe(150)
      expect(data.message).toBe('Inventory updated successfully')
    })

    it('should return validation error for invalid data', async () => {
      const invalidData = {
        currentStock: -10, // Negative stock
        minimumStock: 'invalid', // Should be number
        maximumStock: 5 // Less than minimum
      }

      const existingInventory = {
        id: 1,
        productId: 1,
        currentStock: 100
      }

      mockPrisma.inventory.findUnique.mockResolvedValue(existingInventory as any)

      const response = await PUT(
        new NextRequest('http://localhost:3000/api/inventory/1', {
          method: 'PUT',
          body: JSON.stringify(invalidData)
        }),
        { params: { productId: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Validation failed')
      expect(data.details).toBeDefined()
    })
  })

  describe('PATCH /api/inventory/[productId]', () => {
    it('should adjust stock levels', async () => {
      const adjustmentData = {
        adjustment: 50,
        reason: 'Restocking',
        referenceType: 'PURCHASE',
        referenceId: 123
      }

      const existingInventory = {
        id: 1,
        productId: 1,
        currentStock: 100
      }

      const stockMovement = {
        id: 1,
        productId: 1,
        movementType: 'IN',
        quantity: 50,
        reason: 'Restocking',
        referenceType: 'PURCHASE',
        referenceId: 123,
        createdAt: new Date()
      }

      mockPrisma.inventory.findUnique.mockResolvedValue(existingInventory as any)
      
      // Mock the InventoryService.adjustStock method
      jest.doMock('@/lib/models/inventory', () => ({
        InventoryService: {
          adjustStock: jest.fn().mockResolvedValue(stockMovement)
        }
      }))

      const response = await PATCH(
        new NextRequest('http://localhost:3000/api/inventory/1', {
          method: 'PATCH',
          body: JSON.stringify(adjustmentData)
        }),
        { params: { productId: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toContain('increased successfully')
    })

    it('should return error for zero adjustment', async () => {
      const adjustmentData = {
        adjustment: 0,
        reason: 'No change'
      }

      const response = await PATCH(
        new NextRequest('http://localhost:3000/api/inventory/1', {
          method: 'PATCH',
          body: JSON.stringify(adjustmentData)
        }),
        { params: { productId: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Adjustment must be a non-zero number')
    })

    it('should return error for missing reason', async () => {
      const adjustmentData = {
        adjustment: 10
        // Missing reason
      }

      const response = await PATCH(
        new NextRequest('http://localhost:3000/api/inventory/1', {
          method: 'PATCH',
          body: JSON.stringify(adjustmentData)
        }),
        { params: { productId: '1' } }
      )
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Reason is required')
    })
  })
})
