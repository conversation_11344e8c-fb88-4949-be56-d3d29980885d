'use client'

import { useState, useEffect } from 'react'
import { Package, AlertTriangle } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency } from '@/utils/format'
import { useToast } from '@/hooks/use-toast'
import type { Product } from '@/types'

interface ProductGridProps {
  onAddToCart: (product: Product, quantity: number) => void
  searchTerm: string
  selectedCategory: number | null
}

export function ProductGrid({ onAddToCart, searchTerm, selectedCategory }: ProductGridProps) {
  const [products, setProducts] = useState<(Product & { currentStock: number })[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchProducts()
  }, [searchTerm, selectedCategory])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        limit: '50',
        is_active: 'true'
      })

      if (searchTerm) params.append('search', searchTerm)
      if (selectedCategory) params.append('category_id', selectedCategory.toString())

      const response = await fetch(`/api/products?${params}`)
      const data = await response.json()

      if (data.success) {
        setProducts(data.data.data)
        setError(null)
      } else {
        setError(data.error || 'Failed to fetch products')
      }
    } catch (err) {
      setError('Failed to fetch products')
      console.error('Error fetching products:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleAddToCart = (product: Product & { currentStock: number }) => {
    if (product.currentStock <= 0) {
      toast({
        title: "Out of stock",
        description: "This product is currently out of stock",
        variant: "destructive"
      })
      return
    }
    onAddToCart(product, 1)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <p className="text-lg font-semibold text-destructive">Error</p>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={fetchProducts}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {products.map((product) => (
        <Card
          key={product.id}
          className={`cursor-pointer transition-all hover:shadow-lg ${
            product.currentStock <= 0 ? 'opacity-50' : 'hover:scale-105'
          }`}
          onClick={() => handleAddToCart(product)}
        >
          <CardContent className="p-4">
            <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
              <Package className="h-8 w-8 text-gray-400" />
            </div>
            
            <h3 className="font-semibold text-sm mb-1 line-clamp-2" title={product.name}>
              {product.name}
            </h3>
            
            <p className="text-xs text-muted-foreground mb-2">{product.packagingDescription}</p>
            
            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold text-primary">
                  {formatCurrency(product.unitPrice || product.pricePerPackage / product.unitsPerPackage)}
                </span>
                <span className="text-xs text-muted-foreground">per unit</span>
              </div>
              
              <div className="flex justify-between items-center text-xs">
                <span className="text-muted-foreground">
                  Package: {formatCurrency(product.pricePerPackage)}
                </span>
                <span className="text-muted-foreground">{product.unitsPerPackage} units</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className={`text-xs ${
                  product.currentStock <= 10 ? 'text-destructive' : 'text-green-600'
                }`}>
                  Stock: {product.currentStock}
                </span>
                {product.sku && (
                  <span className="text-xs text-muted-foreground">{product.sku}</span>
                )}
              </div>
            </div>
            
            {product.currentStock <= 0 && (
              <div className="mt-2 text-center">
                <span className="inline-block bg-destructive/10 text-destructive text-xs px-2 py-1 rounded">
                  Out of Stock
                </span>
              </div>
            )}
            
            {product.currentStock > 0 && product.currentStock <= 10 && (
              <div className="mt-2 text-center">
                <span className="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
                  Low Stock
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
      
      {products.length === 0 && (
        <div className="col-span-full text-center py-12">
          <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground mb-2">No products found</h3>
          <p className="text-muted-foreground">
            {searchTerm || selectedCategory
              ? 'Try adjusting your search or filter criteria'
              : 'No products available'}
          </p>
        </div>
      )}
    </div>
  )
}
