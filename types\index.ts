// Database entity types
export interface Category {
  id: number;
  name: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface PackagingType {
  id: number;
  name: string;
  description?: string;
  created_at: Date;
}

export interface Product {
  id: number;
  name: string;
  category_id: number;
  packaging_description: string;
  price_per_package: number;
  units_per_package: number;
  packaging_type_id: number;
  unit_price: number;
  sku?: string;
  barcode?: string;
  description?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  // Joined fields
  category_name?: string;
  packaging_type_name?: string;
}

export interface Inventory {
  id: number;
  product_id: number;
  current_stock: number;
  minimum_stock: number;
  maximum_stock: number;
  last_restocked_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface User {
  id: number;
  username: string;
  email?: string;
  password_hash: string;
  full_name: string;
  role: 'admin' | 'cashier' | 'manager';
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface SalesTransaction {
  id: number;
  transaction_number: string;
  user_id: number;
  total_amount: number;
  tax_amount: number;
  discount_amount: number;
  final_amount: number;
  payment_method: 'cash' | 'card' | 'digital';
  payment_received: number;
  change_amount: number;
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  notes?: string;
  created_at: Date;
  // Joined fields
  user_name?: string;
}

export interface SalesTransactionItem {
  id: number;
  transaction_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: Date;
  // Joined fields
  product_name?: string;
  product_sku?: string;
}

export interface StockMovement {
  id: number;
  product_id: number;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reference_type?: 'sale' | 'restock' | 'adjustment' | 'return';
  reference_id?: number;
  notes?: string;
  user_id?: number;
  created_at: Date;
  // Joined fields
  product_name?: string;
  user_name?: string;
}

// API request/response types
export interface CreateCategoryRequest {
  name: string;
  description?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
}

export interface CreateProductRequest {
  name: string;
  category_id: number;
  packaging_description: string;
  price_per_package: number;
  units_per_package: number;
  packaging_type_id: number;
  sku?: string;
  barcode?: string;
  description?: string;
}

export interface UpdateProductRequest {
  name?: string;
  category_id?: number;
  packaging_description?: string;
  price_per_package?: number;
  units_per_package?: number;
  packaging_type_id?: number;
  sku?: string;
  barcode?: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateInventoryRequest {
  current_stock?: number;
  minimum_stock?: number;
  maximum_stock?: number;
}

export interface CreateUserRequest {
  username: string;
  email?: string;
  password: string;
  full_name: string;
  role: 'admin' | 'cashier' | 'manager';
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  password?: string;
  full_name?: string;
  role?: 'admin' | 'cashier' | 'manager';
  is_active?: boolean;
}

export interface CreateSalesTransactionRequest {
  items: {
    product_id: number;
    quantity: number;
    unit_price: number;
  }[];
  payment_method: 'cash' | 'card' | 'digital';
  payment_received: number;
  discount_amount?: number;
  notes?: string;
}

// Cart and POS types
export interface CartItem {
  product: Product;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
}

// Dashboard and analytics types
export interface SalesAnalytics {
  total_sales: number;
  total_transactions: number;
  average_transaction: number;
  top_products: {
    product_name: string;
    quantity_sold: number;
    revenue: number;
  }[];
  sales_by_category: {
    category_name: string;
    total_sales: number;
    percentage: number;
  }[];
  daily_sales: {
    date: string;
    sales: number;
    transactions: number;
  }[];
}

export interface InventoryAlert {
  product_id: number;
  product_name: string;
  current_stock: number;
  minimum_stock: number;
  status: 'low_stock' | 'out_of_stock';
}

// API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Filter types
export interface ProductFilters extends PaginationParams {
  category_id?: number;
  is_active?: boolean;
  min_price?: number;
  max_price?: number;
  low_stock?: boolean;
}

export interface TransactionFilters extends PaginationParams {
  start_date?: string;
  end_date?: string;
  user_id?: number;
  payment_method?: string;
  status?: string;
}
