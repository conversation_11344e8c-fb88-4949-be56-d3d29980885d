import { prisma } from '@/lib/prisma'
import { Prisma } from '@prisma/client'
import { generateTransactionNumber } from '@/utils/format'
import type { CreateSalesTransactionInput, TransactionFiltersInput } from '@/lib/validations'

export class SalesTransactionService {
  // Create a new sales transaction with items
  static async create(data: CreateSalesTransactionInput, userId: number) {
    return await prisma.$transaction(async (tx) => {
      // Calculate totals
      const subtotal = data.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
      const discountAmount = data.discountAmount || 0
      const taxAmount = (subtotal - discountAmount) * 0.10 // 10% tax
      const finalAmount = subtotal - discountAmount + taxAmount
      const changeAmount = data.paymentReceived - finalAmount

      if (changeAmount < 0) {
        throw new Error('Insufficient payment amount')
      }

      // Generate transaction number
      const transactionNumber = generateTransactionNumber()

      // Create transaction
      const transaction = await tx.salesTransaction.create({
        data: {
          transactionNumber,
          userId,
          totalAmount: subtotal,
          taxAmount,
          discountAmount,
          finalAmount,
          paymentMethod: data.paymentMethod,
          paymentReceived: data.paymentReceived,
          changeAmount,
          notes: data.notes || null
        }
      })

      // Create transaction items and update inventory
      const items = []
      
      for (const item of data.items) {
        // Check stock availability
        const inventory = await tx.inventory.findUnique({
          where: { productId: item.productId }
        })

        if (!inventory || inventory.currentStock < item.quantity) {
          throw new Error(`Insufficient stock for product ID ${item.productId}`)
        }

        // Insert transaction item
        const totalPrice = item.quantity * item.unitPrice
        const transactionItem = await tx.salesTransactionItem.create({
          data: {
            transactionId: transaction.id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice
          },
          include: {
            product: {
              select: { name: true, sku: true }
            }
          }
        })

        items.push(transactionItem)

        // Update inventory
        await tx.inventory.update({
          where: { productId: item.productId },
          data: {
            currentStock: {
              decrement: item.quantity
            }
          }
        })

        // Create stock movement record
        await tx.stockMovement.create({
          data: {
            productId: item.productId,
            movementType: 'OUT',
            quantity: item.quantity,
            referenceType: 'SALE',
            referenceId: transaction.id,
            userId
          }
        })
      }

      return {
        ...transaction,
        items
      }
    })
  }

  // Get transaction by ID with items
  static async getById(id: number) {
    return await prisma.salesTransaction.findUnique({
      where: { id },
      include: {
        user: {
          select: { fullName: true }
        },
        items: {
          include: {
            product: {
              select: { name: true, sku: true }
            }
          },
          orderBy: { id: 'asc' }
        }
      }
    })
  }

  // Get transaction by transaction number
  static async getByTransactionNumber(transactionNumber: string) {
    return await prisma.salesTransaction.findUnique({
      where: { transactionNumber },
      include: {
        user: {
          select: { fullName: true }
        }
      }
    })
  }

  // Get all transactions with filters and pagination
  static async getAll(filters: TransactionFiltersInput = {}) {
    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      sortBy = 'createdAt', 
      sortOrder = 'desc',
      startDate,
      endDate,
      userId,
      paymentMethod,
      status
    } = filters

    const offset = (page - 1) * limit

    const where: Prisma.SalesTransactionWhereInput = {}

    // Search filter
    if (search) {
      where.OR = [
        { transactionNumber: { contains: search, mode: 'insensitive' } },
        { user: { fullName: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // Date range filters
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    // User filter
    if (userId) {
      where.userId = userId
    }

    // Payment method filter
    if (paymentMethod) {
      where.paymentMethod = paymentMethod
    }

    // Status filter
    if (status) {
      where.status = status
    }

    const [transactions, totalItems] = await Promise.all([
      prisma.salesTransaction.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip: offset,
        take: limit,
        include: {
          user: {
            select: { fullName: true }
          }
        }
      }),
      prisma.salesTransaction.count({ where })
    ])

    const totalPages = Math.ceil(totalItems / limit)

    return {
      data: transactions.map(transaction => ({
        ...transaction,
        userName: transaction.user.fullName
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }

  // Update transaction status
  static async updateStatus(id: number, status: 'PENDING' | 'COMPLETED' | 'CANCELLED' | 'REFUNDED') {
    return await prisma.salesTransaction.update({
      where: { id },
      data: { status }
    })
  }

  // Get sales analytics
  static async getSalesAnalytics(startDate?: string, endDate?: string) {
    const where: Prisma.SalesTransactionWhereInput = {
      status: 'COMPLETED'
    }

    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    // Basic sales metrics
    const metrics = await prisma.salesTransaction.aggregate({
      where,
      _sum: {
        finalAmount: true
      },
      _count: true,
      _avg: {
        finalAmount: true
      }
    })

    // Top products
    const topProducts = await prisma.salesTransactionItem.groupBy({
      by: ['productId'],
      where: {
        transaction: where
      },
      _sum: {
        quantity: true,
        totalPrice: true
      },
      orderBy: {
        _sum: {
          totalPrice: 'desc'
        }
      },
      take: 10
    })

    const topProductsWithNames = await Promise.all(
      topProducts.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { name: true }
        })
        return {
          productName: product?.name || 'Unknown',
          quantitySold: item._sum.quantity || 0,
          revenue: item._sum.totalPrice?.toNumber() || 0
        }
      })
    )

    // Sales by category
    const categoryStats = await prisma.salesTransactionItem.groupBy({
      by: ['productId'],
      where: {
        transaction: where
      },
      _sum: {
        totalPrice: true
      }
    })

    const salesByCategory = await Promise.all(
      categoryStats.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          include: { category: { select: { name: true } } }
        })
        return {
          categoryName: product?.category.name || 'Unknown',
          totalSales: item._sum.totalPrice?.toNumber() || 0
        }
      })
    )

    // Group by category and calculate percentages
    const categoryTotals = salesByCategory.reduce((acc, item) => {
      acc[item.categoryName] = (acc[item.categoryName] || 0) + item.totalSales
      return acc
    }, {} as Record<string, number>)

    const totalSales = Object.values(categoryTotals).reduce((sum, val) => sum + val, 0)
    const salesByCategoryFormatted = Object.entries(categoryTotals).map(([name, sales]) => ({
      categoryName: name,
      totalSales: sales,
      percentage: totalSales > 0 ? (sales / totalSales) * 100 : 0
    }))

    // Daily sales (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const dailySales = await prisma.salesTransaction.groupBy({
      by: ['createdAt'],
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        },
        status: 'COMPLETED'
      },
      _sum: {
        finalAmount: true
      },
      _count: true
    })

    const dailySalesFormatted = dailySales.map(day => ({
      date: day.createdAt.toISOString().split('T')[0],
      sales: day._sum.finalAmount?.toNumber() || 0,
      transactions: day._count
    }))

    return {
      totalSales: metrics._sum.finalAmount?.toNumber() || 0,
      totalTransactions: metrics._count,
      averageTransaction: metrics._avg.finalAmount?.toNumber() || 0,
      topProducts: topProductsWithNames,
      salesByCategory: salesByCategoryFormatted,
      dailySales: dailySalesFormatted
    }
  }
}
