import { NextRequest } from 'next/server'

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (request: NextRequest) => string
}

interface RateLimitEntry {
  count: number
  resetTime: number
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, RateLimitEntry>()

// Clean up expired entries every 5 minutes
setInterval(() => {
  const now = Date.now()
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
}, 5 * 60 * 1000)

export function createRateLimiter(config: RateLimitConfig) {
  const { windowMs, maxRequests, keyGenerator } = config

  return function rateLimit(request: NextRequest): {
    success: boolean
    limit: number
    remaining: number
    resetTime: number
    retryAfter?: number
  } {
    // Generate key for this request
    const key = keyGenerator ? keyGenerator(request) : getDef<PERSON><PERSON>ey(request)
    
    const now = Date.now()
    const resetTime = now + windowMs
    
    // Get or create entry
    let entry = rateLimitStore.get(key)
    
    if (!entry || entry.resetTime < now) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime
      }
      rateLimitStore.set(key, entry)
      
      return {
        success: true,
        limit: maxRequests,
        remaining: maxRequests - 1,
        resetTime
      }
    }
    
    // Increment count
    entry.count++
    
    if (entry.count > maxRequests) {
      // Rate limit exceeded
      return {
        success: false,
        limit: maxRequests,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil((entry.resetTime - now) / 1000)
      }
    }
    
    return {
      success: true,
      limit: maxRequests,
      remaining: maxRequests - entry.count,
      resetTime: entry.resetTime
    }
  }
}

function getDefaultKey(request: NextRequest): string {
  // Use IP address as default key
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 
             request.headers.get('x-real-ip') || 
             'unknown'
  
  return `rate_limit:${ip}`
}

// Predefined rate limiters for different use cases
export const rateLimiters = {
  // General API rate limiter: 100 requests per minute
  api: createRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100
  }),
  
  // Strict rate limiter for sensitive operations: 10 requests per minute
  strict: createRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10
  }),
  
  // Auth rate limiter: 5 requests per 15 minutes
  auth: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    keyGenerator: (request) => {
      const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                 request.headers.get('x-real-ip') || 
                 'unknown'
      return `auth_limit:${ip}`
    }
  }),
  
  // Sales transaction rate limiter: 30 requests per minute
  sales: createRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30
  })
}

export function getRateLimitHeaders(result: ReturnType<ReturnType<typeof createRateLimiter>>) {
  return {
    'X-RateLimit-Limit': result.limit.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
    ...(result.retryAfter && {
      'Retry-After': result.retryAfter.toString()
    })
  }
}
