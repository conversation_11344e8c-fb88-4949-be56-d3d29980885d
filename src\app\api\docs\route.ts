import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const apiDocs = {
    title: 'POS Arta API Documentation',
    version: '1.0.0',
    description: 'RESTful API for POS Arta - Modern Point of Sale System',
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
    endpoints: {
      categories: {
        'GET /api/categories': {
          description: 'Get all categories with pagination',
          parameters: {
            page: 'number (optional, default: 1)',
            limit: 'number (optional, default: 50)',
            search: 'string (optional)',
            sort_by: 'string (optional, default: name)',
            sort_order: 'asc|desc (optional, default: asc)'
          },
          response: {
            success: 'boolean',
            data: 'PaginatedResponse<Category[]>',
            timestamp: 'string (ISO date)'
          }
        },
        'POST /api/categories': {
          description: 'Create a new category',
          body: {
            name: 'string (required)',
            description: 'string (optional)'
          },
          response: {
            success: 'boolean',
            data: 'Category',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        },
        'GET /api/categories/[id]': {
          description: 'Get category by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          response: {
            success: 'boolean',
            data: 'Category',
            timestamp: 'string (ISO date)'
          }
        },
        'PUT /api/categories/[id]': {
          description: 'Update category by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          body: {
            name: 'string (optional)',
            description: 'string (optional)'
          },
          response: {
            success: 'boolean',
            data: 'Category',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        },
        'DELETE /api/categories/[id]': {
          description: 'Delete category by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          response: {
            success: 'boolean',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        }
      },
      products: {
        'GET /api/products': {
          description: 'Get all products with filtering and pagination',
          parameters: {
            page: 'number (optional, default: 1)',
            limit: 'number (optional, default: 20)',
            search: 'string (optional)',
            category_id: 'number (optional)',
            is_active: 'boolean (optional)',
            min_price: 'number (optional)',
            max_price: 'number (optional)',
            low_stock: 'boolean (optional)'
          },
          response: {
            success: 'boolean',
            data: 'PaginatedResponse<ProductWithRelations[]>',
            timestamp: 'string (ISO date)'
          }
        },
        'POST /api/products': {
          description: 'Create a new product',
          body: {
            name: 'string (required)',
            categoryId: 'number (required)',
            packagingDescription: 'string (required)',
            pricePerPackage: 'number (required)',
            unitsPerPackage: 'number (required)',
            packagingTypeId: 'number (required)',
            sku: 'string (optional)',
            barcode: 'string (optional)',
            description: 'string (optional)'
          },
          response: {
            success: 'boolean',
            data: 'Product',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        },
        'GET /api/products/[id]': {
          description: 'Get product by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          response: {
            success: 'boolean',
            data: 'ProductWithRelations',
            timestamp: 'string (ISO date)'
          }
        },
        'PUT /api/products/[id]': {
          description: 'Update product by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          body: 'Partial<CreateProductInput>',
          response: {
            success: 'boolean',
            data: 'Product',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        },
        'DELETE /api/products/[id]': {
          description: 'Delete product by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          response: {
            success: 'boolean',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        }
      },
      sales: {
        'GET /api/sales': {
          description: 'Get all sales transactions with filtering',
          parameters: {
            page: 'number (optional, default: 1)',
            limit: 'number (optional, default: 20)',
            search: 'string (optional)',
            start_date: 'string (optional, ISO date)',
            end_date: 'string (optional, ISO date)',
            user_id: 'number (optional)',
            payment_method: 'CASH|CARD|DIGITAL (optional)',
            status: 'PENDING|COMPLETED|CANCELLED|REFUNDED (optional)'
          },
          response: {
            success: 'boolean',
            data: 'PaginatedResponse<SalesTransactionWithRelations[]>',
            timestamp: 'string (ISO date)'
          }
        },
        'POST /api/sales': {
          description: 'Create a new sales transaction',
          body: {
            items: 'Array<{productId: number, quantity: number, unitPrice: number}>',
            paymentMethod: 'CASH|CARD|DIGITAL',
            paymentReceived: 'number',
            discountAmount: 'number (optional)',
            notes: 'string (optional)'
          },
          response: {
            success: 'boolean',
            data: 'SalesTransactionWithRelations',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        },
        'GET /api/sales/[id]': {
          description: 'Get sales transaction by ID',
          parameters: {
            id: 'number (required, path parameter)'
          },
          response: {
            success: 'boolean',
            data: 'SalesTransactionWithRelations',
            timestamp: 'string (ISO date)'
          }
        },
        'PATCH /api/sales/[id]': {
          description: 'Update transaction status',
          parameters: {
            id: 'number (required, path parameter)'
          },
          body: {
            status: 'PENDING|COMPLETED|CANCELLED|REFUNDED',
            notes: 'string (optional)'
          },
          response: {
            success: 'boolean',
            data: 'SalesTransaction',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        }
      },
      inventory: {
        'GET /api/inventory': {
          description: 'Get inventory overview',
          parameters: {
            page: 'number (optional)',
            limit: 'number (optional)',
            low_stock: 'boolean (optional)',
            out_of_stock: 'boolean (optional)'
          },
          response: {
            success: 'boolean',
            data: 'PaginatedResponse<InventoryWithProduct[]>',
            timestamp: 'string (ISO date)'
          }
        },
        'GET /api/inventory/[productId]': {
          description: 'Get inventory for specific product',
          parameters: {
            productId: 'number (required, path parameter)'
          },
          response: {
            success: 'boolean',
            data: 'Inventory',
            timestamp: 'string (ISO date)'
          }
        },
        'PUT /api/inventory/[productId]': {
          description: 'Update inventory levels',
          parameters: {
            productId: 'number (required, path parameter)'
          },
          body: {
            currentStock: 'number (optional)',
            minimumStock: 'number (optional)',
            maximumStock: 'number (optional)'
          },
          response: {
            success: 'boolean',
            data: 'Inventory',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        },
        'PATCH /api/inventory/[productId]': {
          description: 'Adjust stock levels',
          parameters: {
            productId: 'number (required, path parameter)'
          },
          body: {
            adjustment: 'number (required, positive or negative)',
            reason: 'string (required)',
            referenceType: 'MANUAL|SALE|PURCHASE|ADJUSTMENT (optional)',
            referenceId: 'number (optional)'
          },
          response: {
            success: 'boolean',
            data: 'StockMovement',
            message: 'string',
            timestamp: 'string (ISO date)'
          }
        }
      },
      analytics: {
        'GET /api/analytics': {
          description: 'Get analytics dashboard data',
          parameters: {
            period: 'number (optional, days, default: 30)',
            start_date: 'string (optional, ISO date)',
            end_date: 'string (optional, ISO date)'
          },
          response: {
            success: 'boolean',
            data: 'AnalyticsData',
            timestamp: 'string (ISO date)'
          }
        },
        'POST /api/analytics': {
          description: 'Generate specific analytics report',
          body: {
            reportType: 'sales_summary|top_products|sales_by_category|daily_sales|inventory_report|low_stock_report',
            startDate: 'string (optional, ISO date)',
            endDate: 'string (optional, ISO date)',
            filters: 'object (optional)'
          },
          response: {
            success: 'boolean',
            data: 'ReportData',
            timestamp: 'string (ISO date)'
          }
        }
      },
      health: {
        'GET /api/health': {
          description: 'Basic health check',
          response: {
            success: 'boolean',
            data: 'HealthStatus',
            timestamp: 'string (ISO date)'
          }
        },
        'POST /api/health': {
          description: 'Detailed health check',
          body: {
            detailed: 'boolean (optional, default: false)'
          },
          response: {
            success: 'boolean',
            data: 'DetailedHealthStatus',
            timestamp: 'string (ISO date)'
          }
        }
      }
    },
    errorCodes: {
      400: 'Bad Request - Invalid parameters or validation failed',
      401: 'Unauthorized - Authentication required',
      403: 'Forbidden - Insufficient permissions',
      404: 'Not Found - Resource not found',
      409: 'Conflict - Resource already exists',
      422: 'Unprocessable Entity - Invalid data format',
      500: 'Internal Server Error - Server error',
      503: 'Service Unavailable - Service temporarily unavailable'
    },
    responseFormat: {
      success: {
        success: true,
        data: 'ResponseData',
        message: 'string (optional)',
        timestamp: 'string (ISO date)'
      },
      error: {
        success: false,
        error: 'string (error message)',
        details: 'array (optional, validation errors)',
        timestamp: 'string (ISO date)'
      }
    }
  }

  return NextResponse.json(apiDocs, {
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600'
    }
  })
}
