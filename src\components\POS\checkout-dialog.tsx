"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { CartItem, CreateSalesTransactionInput } from "@/types";
import { formatCurrency } from "@/utils/format";
import { zodResolver } from "@hookform/resolvers/zod";
import { Banknote, CreditCard, Smartphone } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const checkoutFormSchema = z.object({
  paymentMethod: z.enum(["CASH", "CARD", "DIGITAL"]),
  paymentReceived: z.number().positive("Payment amount must be positive"),
  discountAmount: z.number().min(0, "Discount cannot be negative").default(0),
  notes: z.string().optional(),
});

type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

interface CheckoutDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cartItems: CartItem[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  total: number;
  onConfirmSale: (
    transactionData: CreateSalesTransactionInput
  ) => Promise<void>;
}

export function CheckoutDialog({
  open,
  onOpenChange,
  cartItems,
  subtotal,
  taxAmount,
  discountAmount,
  total,
  onConfirmSale,
}: CheckoutDialogProps) {
  const [isProcessing, setIsProcessing] = useState(false);

  const form = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: {
      paymentMethod: "CASH",
      paymentReceived: total,
      discountAmount: discountAmount,
      notes: "",
    },
  });

  const watchedValues = form.watch();
  const finalTotal = subtotal - (watchedValues.discountAmount || 0) + taxAmount;
  const changeAmount = (watchedValues.paymentReceived || 0) - finalTotal;

  const onSubmit = async (data: CheckoutFormData) => {
    if (data.paymentReceived < finalTotal) {
      form.setError("paymentReceived", {
        message: "Payment amount is insufficient",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const transactionData: CreateSalesTransactionInput = {
        items: cartItems.map((item) => ({
          productId: item.product.id,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
        paymentMethod: data.paymentMethod,
        paymentReceived: data.paymentReceived,
        discountAmount: data.discountAmount,
        notes: data.notes || undefined,
      };

      await onConfirmSale(transactionData);
      form.reset();
    } catch (error) {
      console.error("Checkout error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleQuickAmount = (amount: number) => {
    form.setValue("paymentReceived", amount);
  };

  const paymentMethods = [
    { value: "CASH" as const, label: "Cash", icon: Banknote },
    { value: "CARD" as const, label: "Card", icon: CreditCard },
    { value: "DIGITAL" as const, label: "Digital", icon: Smartphone },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Checkout</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-3">Order Summary</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal ({cartItems.length} items):</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Discount:</span>
                  <span className="text-green-600">
                    -{formatCurrency(watchedValues.discountAmount || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Tax (10%):</span>
                  <span>{formatCurrency(taxAmount)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total:</span>
                  <span className="text-primary">
                    {formatCurrency(finalTotal)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Discount */}
          <div className="space-y-2">
            <Label htmlFor="discount">Discount Amount</Label>
            <Input
              id="discount"
              type="number"
              min="0"
              max={subtotal}
              step="0.01"
              {...form.register("discountAmount", { valueAsNumber: true })}
              placeholder="0"
            />
            {form.formState.errors.discountAmount && (
              <p className="text-sm text-destructive">
                {form.formState.errors.discountAmount.message}
              </p>
            )}
          </div>

          {/* Payment Method */}
          <div className="space-y-3">
            <Label>Payment Method</Label>
            <div className="grid grid-cols-3 gap-3">
              {paymentMethods.map((method) => {
                const Icon = method.icon;
                return (
                  <Button
                    key={method.value}
                    type="button"
                    variant={
                      watchedValues.paymentMethod === method.value
                        ? "default"
                        : "outline"
                    }
                    className="h-auto p-3 flex flex-col space-y-2"
                    onClick={() => form.setValue("paymentMethod", method.value)}
                  >
                    <Icon className="h-6 w-6" />
                    <span className="text-sm font-medium">{method.label}</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Payment Amount */}
          <div className="space-y-2">
            <Label htmlFor="payment">Payment Received</Label>
            <Input
              id="payment"
              type="number"
              min="0"
              step="0.01"
              {...form.register("paymentReceived", { valueAsNumber: true })}
              placeholder="0"
            />
            {form.formState.errors.paymentReceived && (
              <p className="text-sm text-destructive">
                {form.formState.errors.paymentReceived.message}
              </p>
            )}

            {/* Quick Amount Buttons for Cash */}
            {watchedValues.paymentMethod === "CASH" && (
              <div className="flex flex-wrap gap-2 mt-2">
                {[finalTotal, 50000, 100000, 200000, 500000].map((amount) => (
                  <Button
                    key={amount}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAmount(amount)}
                  >
                    {formatCurrency(amount)}
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Change Amount */}
          {changeAmount >= 0 && (
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-3">
                <div className="flex justify-between items-center">
                  <span className="text-green-800 font-medium">Change:</span>
                  <Badge variant="success" className="text-lg px-3 py-1">
                    {formatCurrency(changeAmount)}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              {...form.register("notes")}
              rows={3}
              placeholder="Add any notes for this transaction..."
            />
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isProcessing ||
                (watchedValues.paymentReceived || 0) < finalTotal
              }
            >
              {isProcessing ? "Processing..." : "Complete Sale"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
