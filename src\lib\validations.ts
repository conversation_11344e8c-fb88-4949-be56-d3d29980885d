import { z } from 'zod'

// Category validation schemas
export const createCategorySchema = z.object({
  name: z.string()
    .min(1, 'Category name is required')
    .max(100, 'Category name must be 100 characters or less')
    .trim(),
  description: z.string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional()
    .nullable()
})

export const updateCategorySchema = z.object({
  name: z.string()
    .min(1, 'Category name is required')
    .max(100, 'Category name must be 100 characters or less')
    .trim()
    .optional(),
  description: z.string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional()
    .nullable()
})

// Product validation schemas
export const createProductSchema = z.object({
  name: z.string()
    .min(1, 'Product name is required')
    .max(255, 'Product name must be 255 characters or less')
    .trim(),
  categoryId: z.number()
    .int('Category ID must be an integer')
    .positive('Category ID must be positive'),
  packagingDescription: z.string()
    .min(1, 'Packaging description is required')
    .max(255, 'Packaging description must be 255 characters or less')
    .trim(),
  pricePerPackage: z.number()
    .positive('Price per package must be positive')
    .max(999999999.99, 'Price per package is too large'),
  unitsPerPackage: z.number()
    .int('Units per package must be an integer')
    .positive('Units per package must be positive'),
  packagingTypeId: z.number()
    .int('Packaging type ID must be an integer')
    .positive('Packaging type ID must be positive'),
  sku: z.string()
    .max(100, 'SKU must be 100 characters or less')
    .trim()
    .optional()
    .nullable(),
  barcode: z.string()
    .max(100, 'Barcode must be 100 characters or less')
    .trim()
    .optional()
    .nullable(),
  description: z.string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional()
    .nullable()
})

export const updateProductSchema = z.object({
  name: z.string()
    .min(1, 'Product name is required')
    .max(255, 'Product name must be 255 characters or less')
    .trim()
    .optional(),
  categoryId: z.number()
    .int('Category ID must be an integer')
    .positive('Category ID must be positive')
    .optional(),
  packagingDescription: z.string()
    .min(1, 'Packaging description is required')
    .max(255, 'Packaging description must be 255 characters or less')
    .trim()
    .optional(),
  pricePerPackage: z.number()
    .positive('Price per package must be positive')
    .max(999999999.99, 'Price per package is too large')
    .optional(),
  unitsPerPackage: z.number()
    .int('Units per package must be an integer')
    .positive('Units per package must be positive')
    .optional(),
  packagingTypeId: z.number()
    .int('Packaging type ID must be an integer')
    .positive('Packaging type ID must be positive')
    .optional(),
  sku: z.string()
    .max(100, 'SKU must be 100 characters or less')
    .trim()
    .optional()
    .nullable(),
  barcode: z.string()
    .max(100, 'Barcode must be 100 characters or less')
    .trim()
    .optional()
    .nullable(),
  description: z.string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional()
    .nullable(),
  isActive: z.boolean().optional()
})

// Inventory validation schemas
export const updateInventorySchema = z.object({
  currentStock: z.number()
    .int('Current stock must be an integer')
    .min(0, 'Current stock cannot be negative')
    .optional(),
  minimumStock: z.number()
    .int('Minimum stock must be an integer')
    .min(0, 'Minimum stock cannot be negative')
    .optional(),
  maximumStock: z.number()
    .int('Maximum stock must be an integer')
    .min(0, 'Maximum stock cannot be negative')
    .optional()
}).refine((data) => {
  if (data.minimumStock !== undefined && data.maximumStock !== undefined) {
    return data.maximumStock >= data.minimumStock
  }
  return true
}, {
  message: 'Maximum stock must be greater than or equal to minimum stock',
  path: ['maximumStock']
})

// User validation schemas
export const createUserSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be 50 characters or less')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
    .trim(),
  email: z.string()
    .email('Invalid email address')
    .max(255, 'Email must be 255 characters or less')
    .optional()
    .nullable(),
  password: z.string()
    .min(6, 'Password must be at least 6 characters')
    .max(100, 'Password must be 100 characters or less'),
  fullName: z.string()
    .min(1, 'Full name is required')
    .max(255, 'Full name must be 255 characters or less')
    .trim(),
  role: z.enum(['ADMIN', 'CASHIER', 'MANAGER'], {
    errorMap: () => ({ message: 'Role must be ADMIN, CASHIER, or MANAGER' })
  })
})

export const updateUserSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be 50 characters or less')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
    .trim()
    .optional(),
  email: z.string()
    .email('Invalid email address')
    .max(255, 'Email must be 255 characters or less')
    .optional()
    .nullable(),
  password: z.string()
    .min(6, 'Password must be at least 6 characters')
    .max(100, 'Password must be 100 characters or less')
    .optional(),
  fullName: z.string()
    .min(1, 'Full name is required')
    .max(255, 'Full name must be 255 characters or less')
    .trim()
    .optional(),
  role: z.enum(['ADMIN', 'CASHIER', 'MANAGER'], {
    errorMap: () => ({ message: 'Role must be ADMIN, CASHIER, or MANAGER' })
  }).optional(),
  isActive: z.boolean().optional()
})

// Sales transaction validation schemas
export const transactionItemSchema = z.object({
  productId: z.number()
    .int('Product ID must be an integer')
    .positive('Product ID must be positive'),
  quantity: z.number()
    .int('Quantity must be an integer')
    .positive('Quantity must be positive'),
  unitPrice: z.number()
    .positive('Unit price must be positive')
    .max(999999999.99, 'Unit price is too large')
})

export const createSalesTransactionSchema = z.object({
  items: z.array(transactionItemSchema)
    .min(1, 'At least one item is required'),
  paymentMethod: z.enum(['CASH', 'CARD', 'DIGITAL'], {
    errorMap: () => ({ message: 'Payment method must be CASH, CARD, or DIGITAL' })
  }),
  paymentReceived: z.number()
    .positive('Payment received must be positive')
    .max(999999999.99, 'Payment received is too large'),
  discountAmount: z.number()
    .min(0, 'Discount amount cannot be negative')
    .max(999999999.99, 'Discount amount is too large')
    .optional()
    .default(0),
  notes: z.string()
    .max(1000, 'Notes must be 1000 characters or less')
    .optional()
    .nullable()
})

export const updateTransactionStatusSchema = z.object({
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED', 'REFUNDED'], {
    errorMap: () => ({ message: 'Status must be PENDING, COMPLETED, CANCELLED, or REFUNDED' })
  })
})

// Pagination and filtering schemas
export const paginationSchema = z.object({
  page: z.number()
    .int('Page must be an integer')
    .positive('Page must be positive')
    .default(1),
  limit: z.number()
    .int('Limit must be an integer')
    .positive('Limit must be positive')
    .max(100, 'Limit cannot exceed 100')
    .default(20),
  search: z.string()
    .max(255, 'Search term must be 255 characters or less')
    .optional()
    .default(''),
  sortBy: z.string()
    .max(50, 'Sort field must be 50 characters or less')
    .optional()
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc'])
    .optional()
    .default('desc')
})

export const productFiltersSchema = paginationSchema.extend({
  categoryId: z.number()
    .int('Category ID must be an integer')
    .positive('Category ID must be positive')
    .optional(),
  isActive: z.boolean().optional(),
  minPrice: z.number()
    .positive('Minimum price must be positive')
    .optional(),
  maxPrice: z.number()
    .positive('Maximum price must be positive')
    .optional(),
  lowStock: z.boolean().optional()
})

export const transactionFiltersSchema = paginationSchema.extend({
  startDate: z.string()
    .datetime('Invalid start date format')
    .optional(),
  endDate: z.string()
    .datetime('Invalid end date format')
    .optional(),
  userId: z.number()
    .int('User ID must be an integer')
    .positive('User ID must be positive')
    .optional(),
  paymentMethod: z.enum(['CASH', 'CARD', 'DIGITAL'])
    .optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED', 'REFUNDED'])
    .optional()
})

// Type exports for use in components
export type CreateCategoryInput = z.infer<typeof createCategorySchema>
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>
export type CreateProductInput = z.infer<typeof createProductSchema>
export type UpdateProductInput = z.infer<typeof updateProductSchema>
export type UpdateInventoryInput = z.infer<typeof updateInventorySchema>
export type CreateUserInput = z.infer<typeof createUserSchema>
export type UpdateUserInput = z.infer<typeof updateUserSchema>
export type CreateSalesTransactionInput = z.infer<typeof createSalesTransactionSchema>
export type UpdateTransactionStatusInput = z.infer<typeof updateTransactionStatusSchema>
export type PaginationInput = z.infer<typeof paginationSchema>
export type ProductFiltersInput = z.infer<typeof productFiltersSchema>
export type TransactionFiltersInput = z.infer<typeof transactionFiltersSchema>
