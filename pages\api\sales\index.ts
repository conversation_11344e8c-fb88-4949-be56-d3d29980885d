import { NextRequest, NextResponse } from "next/server";
import { SalesTransactionModel } from "../../../models/SalesTransaction";
import {
  ApiResponse,
  CreateSalesTransactionRequest,
  TransactionFilters,
} from "../../../types";

export default async function handler(
  req: NextRequest,
  res: NextResponse<ApiResponse>
) {
  try {
    switch (req.method) {
      case "GET":
        await handleGet(req, res);
        break;
      case "POST":
        await handlePost(req, res);
        break;
      default:
        res.setHeader("Allow", ["GET", "POST"]);
        res.status(405).json({
          success: false,
          error: `Method ${req.method} Not Allowed`,
        });
    }
  } catch (error) {
    console.error("Sales API error:", error);
    res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
}

async function handleGet(req: NextRequest, res: NextResponse<ApiResponse>) {
  const {
    page = "1",
    limit = "20",
    search = "",
    sort_by = "created_at",
    sort_order = "desc",
    start_date,
    end_date,
    user_id,
    payment_method,
    status,
  } = req.query;

  const filters: TransactionFilters = {
    page: parseInt(page as string),
    limit: parseInt(limit as string),
    search: search as string,
    sort_by: sort_by as string,
    sort_order: sort_order as "asc" | "desc",
  };

  if (start_date) filters.start_date = start_date as string;
  if (end_date) filters.end_date = end_date as string;
  if (user_id) filters.user_id = parseInt(user_id as string);
  if (payment_method) filters.payment_method = payment_method as string;
  if (status) filters.status = status as string;

  try {
    const result = await SalesTransactionModel.getAll(filters);
    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch transactions",
    });
  }
}

async function handlePost(req: NextRequest, res: NextResponse<ApiResponse>) {
  const {
    items,
    payment_method,
    payment_received,
    discount_amount,
    notes,
  }: CreateSalesTransactionRequest = req.body;

  // Validation
  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      error: "Transaction items are required",
    });
  }

  if (
    !payment_method ||
    !["cash", "card", "digital"].includes(payment_method)
  ) {
    return res.status(400).json({
      success: false,
      error: "Valid payment method is required (cash, card, or digital)",
    });
  }

  if (!payment_received || payment_received <= 0) {
    return res.status(400).json({
      success: false,
      error: "Valid payment amount is required",
    });
  }

  // Validate items
  for (const item of items) {
    if (!item.product_id || !item.quantity || !item.unit_price) {
      return res.status(400).json({
        success: false,
        error: "Each item must have product_id, quantity, and unit_price",
      });
    }

    if (item.quantity <= 0 || item.unit_price <= 0) {
      return res.status(400).json({
        success: false,
        error: "Item quantity and unit price must be greater than 0",
      });
    }
  }

  try {
    // TODO: Get actual user ID from authentication
    // For now, using default cashier user ID (2)
    const userId = 2;

    const transaction = await SalesTransactionModel.create(
      {
        items,
        payment_method,
        payment_received,
        discount_amount,
        notes,
      },
      userId
    );

    res.status(201).json({
      success: true,
      data: transaction,
      message: "Transaction created successfully",
    });
  } catch (error) {
    console.error("Error creating transaction:", error);

    if (error instanceof Error) {
      if (error.message.includes("Insufficient stock")) {
        return res.status(409).json({
          success: false,
          error: error.message,
        });
      }

      if (error.message.includes("Insufficient payment")) {
        return res.status(400).json({
          success: false,
          error: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      error: "Failed to create transaction",
    });
  }
}
